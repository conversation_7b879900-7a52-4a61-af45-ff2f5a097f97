package com.bulkloads.web.usda.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.Attachment;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.usda.repository.UsdaRepository;
import com.bulkloads.web.usda.service.dto.UsdaDataResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsdaService {

  private final UsdaRepository usdaRepository;
  private final UsdaCsvService usdaCsvService;
  private final EmailService emailService;
  private final AppProperties appProperties;

  public void generateAndEmailUsdaReport() throws IOException {
    log.info("Starting USDA report generation");

    final List<UsdaDataResponse> data = usdaRepository.generateReportData(500);
    log.info("Retrieved {} records for USDA report", data.size());

    if (data.isEmpty()) {
      log.warn("No data found for USDA report, skipping email");
      return;
    }

    final Path csvFile = usdaCsvService.generateCsvFile(data);

    sendEmailWithCsvAttachment(csvFile);

    Files.deleteIfExists(csvFile);
    log.info("USDA report generation and email completed successfully");
  }

  private void sendEmailWithCsvAttachment(Path csvFile) throws IOException {
    final String reportEmailAddress = appProperties.getUsda().getReportEmailAddress();
    final AppProperties.Mailing mailing = appProperties.getMailing();
    final String fromEmail = mailing.getFromEmail();
    final String replyToEmail = mailing.getReplyToEmail();
    final String failToEmail = mailing.getFailToEmail();

    if (reportEmailAddress == null || reportEmailAddress.trim().isEmpty()) {
      log.error("USDA report email address not configured");
      throw new IllegalStateException("USDA report email address not configured");
    }

    final String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    final String subject = "USDA Load Report - %s".formatted(timestamp);
    final String message = """
            Please find attached the USDA load report generated on %s.
            
            This report contains load data for the past 4 weeks grouped by region, week, and mileage range.
            
            Best regards,
            
            Bulkloads System""".formatted(timestamp);

    byte[] csvBytes = Files.readAllBytes(csvFile);
    String base64 = Base64.getEncoder().encodeToString(csvBytes);
    String dataUrl = "data:text/csv;base64," + base64;

    Attachment attachment = Attachment.builder()
        .filename(csvFile.getFileName().toString())
        .url(dataUrl)
        .build();

    EmailDetails emailDetails = EmailDetails.builder()
        .fromEmail(fromEmail)
        .toEmails(List.of(reportEmailAddress))
        .replyToEmail(replyToEmail)
        .failTo(failToEmail)
        .subject(subject)
        .message(message)
        .attachments(List.of(attachment))
        .description("USDA Load Report")
        .build();

    Optional<Integer> emailId = emailService.sendEmail(emailDetails);

    if (emailId.isPresent()) {
      log.info("USDA report email sent successfully with ID: {}", emailId.get());
    } else {
      log.warn("USDA report email was not sent (possibly filtered or duplicate)");
    }
  }
}
