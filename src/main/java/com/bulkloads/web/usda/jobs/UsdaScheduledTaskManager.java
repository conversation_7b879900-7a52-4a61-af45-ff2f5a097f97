package com.bulkloads.web.usda.jobs;

import java.io.IOException;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import com.bulkloads.web.usda.service.UsdaService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class UsdaScheduledTaskManager {

  private final UsdaService usdaService;

//  @Scheduled(cron = "0 0 8 * * MON")
  @Scheduled(cron = "1/20 * * * * *")
  @SchedulerLock(name = "sendUsdaDataReport", lockAtMostFor = "30m", lockAtLeastFor = "5m")
  public void sendUsdaDataReport() throws IOException {
    long start = System.currentTimeMillis();
    usdaService.generateAndEmailUsdaReport();
    long duration = System.currentTimeMillis() - start;
    log.info("Executed sendUsdaDataReport scheduled task. took: {} (s)", duration / 1000);
  }
}
