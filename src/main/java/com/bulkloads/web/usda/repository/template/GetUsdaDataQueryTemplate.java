package com.bulkloads.web.usda.repository.template;

import org.intellij.lang.annotations.Language;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class GetUsdaDataQueryTemplate {

  @Language("SQL")
  public static final String GET_USDA_DATA_QUERY_TEMPLATE = """
      SELECT r.type                 AS region,
             subquery.week_of       AS week_of,
             subquery.mileage       AS mileage,
             subquery.rate_per_mile AS rate_per_mile,
             COUNT(*)               AS number_of_Loads
      FROM (SELECT lii.load_assignment_id,
                   lii.pickup_state,
                   date_format(str_to_date(concat(yearweek(lii.hauled_date), ' Monday'), '%X%V %W'), '%m/%d/%Y') AS week_of,
                   CASE
                       WHEN lii.bill_miles <= 100 THEN '1-100'
                       WHEN lii.bill_miles <= 200 THEN '101-200'
                       ELSE '200+'
                       END                                                                                       as mileage,
                   round(sum(lii.item_amount) / lii.bill_miles, 6)                                               AS rate_per_mile
            FROM load_invoice_items lii
            WHERE lii.deleted = 0
              AND lii.hauled_date IS NOT NULL
              AND lii.bill_miles > 0
              AND lii.bill_rate > 0
              AND yearweek(lii.hauled_date) >= yearweek(curdate(), 1) - :noOfWeeks
              AND yearweek(lii.hauled_date) < yearweek(curdate(), 1)
              AND (lii.is_surcharge = 0 OR lii.load_assignment_surcharge_type_id IN (1, 2, 12))
            GROUP BY lii.load_assignment_id) subquery
               JOIN load_assignments la ON subquery.load_assignment_id = la.load_assignment_id
               JOIN states s ON subquery.pickup_state = s.abbreviation
               JOIN regions r ON s.region = r.id
      WHERE la.parent_load_assignment_id IS NULL
        AND s.country = 'US'
      GROUP BY r.type,
               subquery.week_of,
               subquery.mileage
      ORDER BY subquery.week_of,
               r.type,
               subquery.mileage
      """;

}
