package com.bulkloads.web.usda.repository;

import static com.bulkloads.web.usda.repository.template.GetUsdaDataQueryTemplate.GET_USDA_DATA_QUERY_TEMPLATE;

import java.util.List;
import java.util.Map;
import com.bulkloads.web.usda.service.dto.UsdaDataResponse;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.simple.JdbcClient;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class UsdaRepositoryImpl implements UsdaRepository {

  private final JdbcClient jdbcClient;

  @Override
  public List<UsdaDataResponse> generateReportData(final int noOfWeeks) {

    final Map<String, Integer> queryParams = Map.of("noOfWeeks", noOfWeeks);

    return jdbcClient
        .sql(GET_USDA_DATA_QUERY_TEMPLATE)
        .params(queryParams)
        .query(getRowMapper())
        .list();
  }

  private RowMapper<UsdaDataResponse> getRowMapper() {
    return (rs, rowNum) -> UsdaDataResponse.builder()
        .region(rs.getString("region"))
        .weekOf(rs.getString("week_of"))
        .mileage(rs.getString("mileage"))
        .ratePerMile(rs.getDouble("rate_per_mile"))
        .numberOfLoads(rs.getInt("number_of_loads"))
        .build();
  }
}
