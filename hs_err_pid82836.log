#
# A fatal error has been detected by the Java Runtime Environment:
#
#  Internal Error (sharedRuntime.cpp:555), pid=82836, tid=97043
#  guarantee(cb != NULL && cb->is_compiled()) failed: safepoint polling: pc must refer to an nmethod
#
# JRE version: OpenJDK Runtime Environment JBR-17.0.9+7-1087.7-nomod (17.0.9+7) (build 17.0.9+7-b1087.7)
# Java VM: OpenJDK 64-Bit Server VM JBR-17.0.9+7-1087.7-nomod (17.0.9+7-b1087.7, mixed mode, emulated-client, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-amd64)
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:61321,suspend=y,server=n -Xmx4G -XX:ReservedCodeCacheSize=256m -Dbulkloads.domain-url=https://curiously-charming-firefly.ngrok-free.app -agentpath:/private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/WebApplication_2025_08_05_220549.jfr,log=/private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/WebApplication_2025_08_05_220549.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dfile.encoding=UTF-8 com.bulkloads.WebApplication

Host: "Mac15,3" x86_64 (EMULATED) 2400 MHz, 8 cores, 16G, Darwin 24.3.0, macOS 15.3 (24D60)
Time: Tue Aug  5 22:08:01 2025 EEST elapsed time: 131.906545 seconds (0d 0h 2m 11s)

---------------  T H R E A D  ---------------

Current thread (0x00007f9d8884b000):  JavaThread "scheduling-1" [_thread_in_Java, id=97043, stack(0x0000000310c70000,0x0000000310d70000)]

Stack: [0x0000000310c70000,0x0000000310d70000],  sp=0x0000000310d6b8f0,  free space=1006k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [libjvm.dylib+0xb296b6]  VMError::report_and_die(int, char const*, char const*, __va_list_tag*, Thread*, unsigned char*, void*, void*, char const*, int, unsigned long)+0x696
V  [libjvm.dylib+0xb29c68]  VMError::report_and_die(Thread*, void*, char const*, int, char const*, char const*, __va_list_tag*)+0x38
V  [libjvm.dylib+0x36f03e]  report_vm_error(char const*, int, char const*, char const*, ...)+0xce
V  [libjvm.dylib+0x95dab5]  SharedRuntime::get_poll_stub(unsigned char*)+0x45
V  [libjvm.dylib+0x8c8a0c]  PosixSignals::pd_hotspot_signal_handler(int, __siginfo*, __darwin_ucontext*, JavaThread*)+0x12c
V  [libjvm.dylib+0x9eaaec]  JVM_handle_bsd_signal+0xfc
C  [libasyncProfiler.dylib+0x5130f]  Profiler::segvHandler(int, __siginfo*, void*)+0x9f
C  [libsystem_platform.dylib+0x3e1d]  _sigtramp+0x1d
V  [libjvm.dylib+0x95f8da]  SharedRuntime::find_callee_info(Bytecodes::Code&, CallInfo&, JavaThread*)+0x19a
V  [libjvm.dylib+0x52e892]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x2a2
V  [libjvm.dylib+0x52d9d5]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0xf5
V  [libjvm.dylib+0x52db30]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, Handle, JavaThread*)+0x70
V  [libjvm.dylib+0xa6e92d]  SystemDictionary::load_instance_class_impl(Symbol*, Handle, JavaThread*)+0x11d
V  [libjvm.dylib+0xa6d3a5]  SystemDictionary::load_instance_class(unsigned int, Symbol*, Handle, JavaThread*)+0x25
V  [libjvm.dylib+0xa6c94e]  SystemDictionary::resolve_instance_class_or_null(Symbol*, Handle, Handle, JavaThread*)+0x57e
V  [libjvm.dylib+0xa6be94]  SystemDictionary::resolve_or_fail(Symbol*, Handle, Handle, bool, JavaThread*)+0x64
V  [libjvm.dylib+0x35e197]  ConstantPool::klass_at_impl(constantPoolHandle const&, int, JavaThread*)+0x1a7
V  [libjvm.dylib+0x35ed71]  ConstantPool::klass_ref_at(int, JavaThread*)+0x71
V  [libjvm.dylib+0x749243]  LinkInfo::LinkInfo(constantPoolHandle const&, int, JavaThread*)+0x33
V  [libjvm.dylib+0x74e045]  LinkResolver::resolve_invoke(CallInfo&, Handle, constantPoolHandle const&, int, Bytecodes::Code, JavaThread*)+0x65
V  [libjvm.dylib+0x527ea2]  InterpreterRuntime::resolve_invoke(JavaThread*, Bytecodes::Code)+0x2f2
V  [libjvm.dylib+0x528409]  InterpreterRuntime::resolve_from_cache(JavaThread*, Bytecodes::Code)+0x59
j  net.javacrumbs.shedlock.core.DefaultLockingTaskExecutor.executeWithLock(Lnet/javacrumbs/shedlock/core/LockingTaskExecutor$TaskWithResult;Lnet/javacrumbs/shedlock/core/LockConfiguration;)Lnet/javacrumbs/shedlock/core/LockingTaskExecutor$TaskResult;+323
j  net.javacrumbs.shedlock.spring.aop.MethodProxyScheduledLockAdvisor$LockingInterceptor.invoke(Lorg/aopalliance/intercept/MethodInvocation;)Ljava/lang/Object;+125
j  org.springframework.aop.framework.ReflectiveMethodInvocation.proceed()Ljava/lang/Object;+120
j  org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed()Ljava/lang/Object;+1
j  org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;Lorg/springframework/cglib/proxy/MethodProxy;)Ljava/lang/Object;+122
j  com.bulkloads.web.usda.jobs.UsdaScheduledTaskManager$$SpringCGLIB$$0.sendUsdaDataReport()V+31
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x52e892]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x2a2
V  [libjvm.dylib+0x93888d]  invoke(InstanceKlass*, methodHandle const&, Handle, bool, objArrayHandle, BasicType, objArrayHandle, bool, JavaThread*)+0xa6d
V  [libjvm.dylib+0x937dcf]  Reflection::invoke_method(oopDesc*, Handle, objArrayHandle, JavaThread*)+0x12f
V  [libjvm.dylib+0x5ddb9e]  JVM_InvokeMethod+0x30e
J 5789  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.9 (0 bytes) @ 0x0000000112f0d4b5 [0x0000000112f0d3c0+0x00000000000000f5]
J 5788 c1 jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.9 (137 bytes) @ 0x0000000112f0e5ac [0x0000000112f0e280+0x000000000000032c]
J 4531 c1 jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.9 (10 bytes) @ 0x0000000112c2c724 [0x0000000112c2c6c0+0x0000000000000064]
J 4532 c1 java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.9 (65 bytes) @ 0x0000000112c2cb84 [0x0000000112c2ca60+0x0000000000000124]
j  org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(Lorg/springframework/scheduling/support/ScheduledTaskObservationContext;)V+19
j  org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(Lorg/springframework/scheduling/support/ScheduledTaskObservationContext;)V+2
j  org.springframework.scheduling.support.ScheduledMethodRunnable$$Lambda$2918+0x00000008014379e0.run()V+8
j  io.micrometer.observation.Observation.observe(Ljava/lang/Runnable;)V+15
j  org.springframework.scheduling.support.ScheduledMethodRunnable.run()V+53
j  org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run()V+4
j  org.springframework.scheduling.concurrent.ReschedulingRunnable.run()V+12
j  java.util.concurrent.Executors$RunnableAdapter.call()Ljava/lang/Object;+4 java.base@17.0.9
j  java.util.concurrent.FutureTask.run()V+39 java.base@17.0.9
j  java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run()V+28 java.base@17.0.9
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@17.0.9
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@17.0.9
j  java.lang.Thread.run()V+11 java.base@17.0.9
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x52e892]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x2a2
V  [libjvm.dylib+0x52d9d5]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0xf5
V  [libjvm.dylib+0x52da93]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, JavaThread*)+0x63
V  [libjvm.dylib+0x5d99a4]  thread_entry(JavaThread*, JavaThread*)+0xb4
V  [libjvm.dylib+0xab3390]  JavaThread::thread_main_inner()+0x150
V  [libjvm.dylib+0xab16df]  Thread::call_run()+0xbf
V  [libjvm.dylib+0x8c54e8]  thread_native_entry(Thread*)+0x148
C  [libsystem_pthread.dylib+0x6253]  _pthread_start+0x63
C  [libsystem_pthread.dylib+0x1bef]  thread_start+0xf


Compiled method (n/a)  131914 5789     n 0       jdk.internal.reflect.NativeMethodAccessorImpl::invoke0 (native)
 total in heap  [0x0000000112f0d210,0x0000000112f0d650] = 1088
 relocation     [0x0000000112f0d370,0x0000000112f0d3a8] = 56
 main code      [0x0000000112f0d3c0,0x0000000112f0d650] = 656

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000000012e346b50} 'invoke0' '(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;' in 'jdk/internal/reflect/NativeMethodAccessorImpl'
  # parm0:    rsi:rsi   = 'java/lang/reflect/Method'
  # parm1:    rdx:rdx   = 'java/lang/Object'
  # parm2:    rcx:rcx   = '[Ljava/lang/Object;'
  #           [sp+0x50]  (sp of caller)
  0x0000000112f0d3c0: 448b 5608 | 49bb 0000 | 0000 0800 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x0000000112f0d3d8: ;   {runtime_call ic_miss_stub}
  0x0000000112f0d3d8: 0000 e9a1 | d933 ff90 
[Verified Entry Point]
  0x0000000112f0d3e0: 8984 2400 | c0fe ff55 | 488b ec48 | 83ec 4048 | 894c 2410 | 4883 f900 | 4c8d 4424 | 104c 0f44 
  0x0000000112f0d400: 4424 1048 | 8954 2408 | 4883 fa00 | 488d 4c24 | 0848 0f44 | 4c24 0848 | 8934 2448 | 83fe 0048 
  0x0000000112f0d420: 8d14 2448 | 0f44 1424 

  0x0000000112f0d428: ;   {oop(a 'java/lang/Class'{0x0000000702800790} = 'jdk/internal/reflect/NativeMethodAccessorImpl')}
  0x0000000112f0d428: 49be 9007 | 8002 0700 | 0000 4c89 | 7424 304c | 8d74 2430 

  0x0000000112f0d43c: ; ImmutableOopMap {[16]=Oop [8]=Oop [0]=Oop [48]=Oop }
                      ;   {section_word}
  0x0000000112f0d43c: 498b f649 | ba3f d4f0 | 1201 0000 | 004d 8997 | 9802 0000 | 4989 a790 

  0x0000000112f0d454: ;   {external_word}
  0x0000000112f0d454: 0200 0080 | 3d34 88e3 | f900 0f84 | 3a00 0000 | 5652 5141 

  0x0000000112f0d468: ;   {metadata({method} {0x000000012e346b50} 'invoke0' '(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;' in 'jdk/internal/reflect/NativeMethodAccessorImpl')}
  0x0000000112f0d468: 5048 be50 | 6b34 2e01 | 0000 0049 | 8bff f7c4 | 0f00 0000 | 0f84 1200 | 0000 4883 

  0x0000000112f0d484: ;   {runtime_call SharedRuntime::dtrace_method_entry(JavaThread*, Method*)}
  0x0000000112f0d484: ec08 e855 | 01a2 f948 | 83c4 08e9 | 0500 0000 

  0x0000000112f0d494: ;   {runtime_call SharedRuntime::dtrace_method_entry(JavaThread*, Method*)}
  0x0000000112f0d494: e847 01a2 | f941 5859 | 5a5e 498d | bfb0 0200 | 0041 c787 | 4003 0000 | 0400 0000 

  0x0000000112f0d4b0: ;   {runtime_call Java_jdk_internal_reflect_NativeMethodAccessorImpl_invoke0}
  0x0000000112f0d4b0: e86b a70c | f841 c787 | 4003 0000 | 0500 0000 | f083 4424 | c000 493b | af48 0300 | 000f 8711 
  0x0000000112f0d4d0: 0000 0041 | 81bf 3003 | 0000 0000 | 0000 0f84 | 2100 0000 | 4889 45f8 | 498b ff4c | 8be4 4883 
  0x0000000112f0d4f0: ec00 4883 

  0x0000000112f0d4f4: ;   {runtime_call JavaThread::check_special_condition_for_native_trans(JavaThread*)}
  0x0000000112f0d4f4: e4f0 e835 | 5ab7 f949 | 8be4 4d33 | e448 8b45 | f841 c787 | 4003 0000 | 0800 0000 | 4181 bfb0 
  0x0000000112f0d514: 0300 0002 | 0000 000f | 8405 0100 

  0x0000000112f0d520: ;   {external_word}
  0x0000000112f0d520: 0080 3d6a | 87e3 f900 | 0f84 3800 | 0000 4889 

  0x0000000112f0d530: ;   {metadata({method} {0x000000012e346b50} 'invoke0' '(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;' in 'jdk/internal/reflect/NativeMethodAccessorImpl')}
  0x0000000112f0d530: 45f8 48be | 506b 342e | 0100 0000 | 498b fff7 | c40f 0000 | 000f 8412 | 0000 0048 

  0x0000000112f0d54c: ;   {runtime_call SharedRuntime::dtrace_method_exit(JavaThread*, Method*)}
  0x0000000112f0d54c: 83ec 08e8 | 3c01 a2f9 | 4883 c408 | e905 0000 

  0x0000000112f0d55c: ;   {runtime_call SharedRuntime::dtrace_method_exit(JavaThread*, Method*)}
  0x0000000112f0d55c: 00e8 2e01 | a2f9 488b | 45f8 49c7 | 8790 0200 | 0000 0000 | 0049 c787 | 9802 0000 | 0000 0000 
  0x0000000112f0d57c: 4885 c00f | 847b 0000 | 0048 f7c0 | 0100 0000 | 0f84 6b00 | 0000 488b | 40ff 4180 | 7f38 000f 
  0x0000000112f0d59c: 8457 0000 | 0048 83f8 | 000f 844d | 0000 0049 | 8b4f 2048 | 83f9 000f | 8414 0000 | 0048 83e9 
  0x0000000112f0d5bc: 0849 894f | 2049 034f | 3048 8901 | e92b 0000 | 0050 498b | f748 8bf8 | f7c4 0f00 | 0000 0f84 
  0x0000000112f0d5dc: 1200 0000 | 4883 ec08 

  0x0000000112f0d5e4: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x0000000112f0d5e4: e8d7 134e | f948 83c4 | 08e9 0500 

  0x0000000112f0d5f0: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x0000000112f0d5f0: 0000 e8c9 | 134e f958 | e903 0000 | 0048 8b00 | 498b 8fd8 | 0000 00c7 | 8100 0100 | 0000 0000 
  0x0000000112f0d610: 00c9 4981 | 7f08 0000 | 0000 0f85 | 0100 0000 

  0x0000000112f0d620: ;   {runtime_call StubRoutines (1)}
  0x0000000112f0d620: c3e9 dad5 | 2eff 4889 | 45f8 4c8b | e448 83ec | 0048 83e4 

  0x0000000112f0d634: ;   {runtime_call SharedRuntime::reguard_yellow_pages()}
  0x0000000112f0d634: f0e8 5622 | a2f9 498b | e44d 33e4 | 488b 45f8 | e9d8 feff | fff4 f4f4 | f4f4 f4f4 
[/MachCode]


Compiled method (c1)  131918 5788   !   1       jdk.internal.reflect.NativeMethodAccessorImpl::invoke (137 bytes)
 total in heap  [0x0000000112f0e090,0x0000000112f0efe8] = 3928
 relocation     [0x0000000112f0e1f0,0x0000000112f0e280] = 144
 main code      [0x0000000112f0e280,0x0000000112f0e7c0] = 1344
 stub code      [0x0000000112f0e7c0,0x0000000112f0e840] = 128
 metadata       [0x0000000112f0e840,0x0000000112f0e8b0] = 112
 scopes data    [0x0000000112f0e8b0,0x0000000112f0eab8] = 520
 scopes pcs     [0x0000000112f0eab8,0x0000000112f0ee68] = 944
 dependencies   [0x0000000112f0ee68,0x0000000112f0ee88] = 32
 handler table  [0x0000000112f0ee88,0x0000000112f0efa8] = 288
 nul chk table  [0x0000000112f0efa8,0x0000000112f0efe8] = 64

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000000012e3469f8} 'invoke' '(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;' in 'jdk/internal/reflect/NativeMethodAccessorImpl'
  # this:     rsi:rsi   = 'jdk/internal/reflect/NativeMethodAccessorImpl'
  # parm0:    rdx:rdx   = 'java/lang/Object'
  # parm1:    rcx:rcx   = '[Ljava/lang/Object;'
  #           [sp+0xe0]  (sp of caller)
  0x0000000112f0e280: 448b 5608 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x0000000112f0e294: ;   {runtime_call ic_miss_stub}
  0x0000000112f0e294: 0f85 e6ca | 33ff 660f | 1f44 0000 
[Verified Entry Point]
  0x0000000112f0e2a0: 8984 2400 | c0fe ff55 | 4881 ecd0 

  0x0000000112f0e2ac: ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@0 (line 56)
  0x0000000112f0e2ac: 0000 0048 | 8974 2470 

  0x0000000112f0e2b4: ;*getfield numInvocations {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@2 (line 56)
  0x0000000112f0e2b4: 8b7e 0cff 

  0x0000000112f0e2b8: ;*putfield numInvocations {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@8 (line 56)
  0x0000000112f0e2b8: c789 7e0c 

  0x0000000112f0e2bc: ;   {oop(a 'java/lang/Class'{0x00000007028023b0} = 'jdk/internal/reflect/ReflectionFactory')}
  0x0000000112f0e2bc: 48bb b023 | 8002 0700 | 0000 8b5b 

  0x0000000112f0e2c8: ;*getstatic inflationThreshold {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.ReflectionFactory::inflationThreshold@0 (line 632)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@11 (line 56)
  0x0000000112f0e2c8: 783b fb48 | 898c 2480 | 0000 0048 | 8954 2478 | 0f8f 0800 

  0x0000000112f0e2dc: ;*if_icmple {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@14 (line 56)
  0x0000000112f0e2dc: 0000 488b | fee9 ad02 

  0x0000000112f0e2e4: ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@127 (line 77)
  0x0000000112f0e2e4: 0000 8b7e | 1448 c1e7 

  0x0000000112f0e2ec: ;*getfield method {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@18 (line 56)
  0x0000000112f0e2ec: 038b 7f24 

  0x0000000112f0e2f0: ;*getfield clazz {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getDeclaringClass@1 (line 223)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@21 (line 57)
  0x0000000112f0e2f0: 48c1 e703 

  0x0000000112f0e2f4: ; implicit exception: dispatches to 0x0000000112f0e60b
  0x0000000112f0e2f4: 483b 0748 

  0x0000000112f0e2f8: ;*invokevirtual isHidden {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@24 (line 57)
  0x0000000112f0e2f8: 8bf7 0f1f 

  0x0000000112f0e2fc: ;   {optimized virtual_call}
  0x0000000112f0e2fc: 4400 00e8 

  0x0000000112f0e300: ; ImmutableOopMap {[112]=Oop [128]=Oop [120]=Oop }
                      ;*invokevirtual isHidden {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@24 (line 57)
  0x0000000112f0e300: 3c0b 57ff | 83f8 000f | 840a 0000 

  0x0000000112f0e30c: ;*ifne {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@27 (line 57)
  0x0000000112f0e30c: 0048 8b7c | 2470 e97c 

  0x0000000112f0e314: ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@127 (line 77)
  0x0000000112f0e314: 0200 0048 | 8b74 2470 

  0x0000000112f0e31c: ;*getfield generated {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@31 (line 57)
  0x0000000112f0e31c: 8b46 1083 | f800 0f84 

  0x0000000112f0e324: ;*ifne {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@34 (line 57)
  0x0000000112f0e324: 0800 0000 | 488b fee9 

  0x0000000112f0e32c: ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@127 (line 77)
  0x0000000112f0e32c: 6302 0000 | 488d 5610 | b800 0000 | 00bf 0100 | 0000 f00f | b13a ba01 | 0000 000f | 8405 0000 
  0x0000000112f0e34c: 00ba 0000 

  0x0000000112f0e350: ;*invokevirtual compareAndSetInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@46 (line 59)
  0x0000000112f0e350: 0000 83fa | 000f 8508 

  0x0000000112f0e358: ;*ifeq {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@49 (line 59)
  0x0000000112f0e358: 0000 0048 | 8bfe e930 

  0x0000000112f0e360: ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@127 (line 77)
                      ;   {metadata('jdk/internal/reflect/MethodAccessorGenerator')}
  0x0000000112f0e360: 0200 0048 | ba80 0f16 | 0008 0000 | 0048 8bde | 498b 8708 | 0100 0048 | 8db8 9000 | 0000 493b 
  0x0000000112f0e380: bf18 0100 | 000f 8785 | 0200 0049 | 89bf 0801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 
  0x0000000112f0e3a0: 0000 0800 | 0000 492b | ca89 4808 | 4833 c989 | 480c 4833 | c948 bf10 | 0000 0000 | 0000 0048 
  0x0000000112f0e3c0: 894c f808 | 48ff cf75 

  0x0000000112f0e3c8: ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@52 (line 61)
  0x0000000112f0e3c8: f6be 1e00 | 0000 6689 

  0x0000000112f0e3d0: ;*putfield NUM_COMMON_CPOOL_ENTRIES {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.AccessorGenerator::<init>@7 (line 98)
                      ; - jdk.internal.reflect.MethodAccessorGenerator::<init>@1 (line 64)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@56 (line 61)
  0x0000000112f0e3d0: 706a be49 | 0000 0066 

  0x0000000112f0e3d8: ;*putfield NUM_BOXING_CPOOL_ENTRIES {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.AccessorGenerator::<init>@13 (line 99)
                      ; - jdk.internal.reflect.MethodAccessorGenerator::<init>@1 (line 64)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@56 (line 61)
  0x0000000112f0e3d8: 8970 6c8b | 7b14 48c1 

  0x0000000112f0e3e0: ;*getfield method {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@60 (line 61)
  0x0000000112f0e3e0: e703 8b57 | 2448 c1e2 

  0x0000000112f0e3e8: ;*getfield clazz {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getDeclaringClass@1 (line 223)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@63 (line 63)
  0x0000000112f0e3e8: 038b 4f28 

  0x0000000112f0e3ec: ;*getfield name {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getName@1 (line 232)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@70 (line 64)
  0x0000000112f0e3ec: 48c1 e103 | 8b77 3048 

  0x0000000112f0e3f4: ;*getfield parameterTypes {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getParameterTypes@1 (line 314)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@77 (line 65)
  0x0000000112f0e3f4: c1e6 0348 | 89bc 24a0 | 0000 0048 | 8984 2498 | 0000 0048 | 898c 2490 | 0000 0048 | 8994 2488 
  0x0000000112f0e414: 0000 0066 | 0f1f 4400 | 0048 b8a0 | ab05 0008 

  0x0000000112f0e424: ;   {virtual_call}
  0x0000000112f0e424: 0000 00e8 

  0x0000000112f0e428: ; ImmutableOopMap {[128]=Oop [120]=Oop [144]=Oop [160]=Oop [152]=Oop [112]=Oop [136]=Oop }
                      ;*invokevirtual clone {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getParameterTypes@4 (line 314)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@77 (line 65)
  0x0000000112f0e428: 14b0 4eff | 4883 f800 | 0f84 2900 

  0x0000000112f0e434: ;   {metadata('java/lang/Class'[])}
  0x0000000112f0e434: 0000 48bb | a0ab 0500 | 0800 0000 | 8b78 0849 | ba00 0000 | 0008 0000 | 0049 03fa | 483b 5f40 
  0x0000000112f0e454: 0f85 c801 | 0000 e900 | 0000 0048 

  0x0000000112f0e460: ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getParameterTypes@7 (line 314)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@77 (line 65)
  0x0000000112f0e460: 8bf8 488b | 7424 708b | 5e14 48c1 

  0x0000000112f0e46c: ;*getfield method {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@81 (line 65)
  0x0000000112f0e46c: e303 8b43 | 2c48 c1e0 

  0x0000000112f0e474: ;*getfield returnType {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getReturnType@1 (line 266)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@84 (line 66)
  0x0000000112f0e474: 038b 5334 

  0x0000000112f0e478: ;*getfield exceptionTypes {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getExceptionTypes@1 (line 341)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@91 (line 67)
  0x0000000112f0e478: 48c1 e203 

  0x0000000112f0e47c: ;*invokevirtual clone {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getExceptionTypes@4 (line 341)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@91 (line 67)
  0x0000000112f0e47c: 488b f248 | 899c 24b8 | 0000 0048 | 8984 24b0 | 0000 0048 | 89bc 24a8 | 0000 0066 | 0f1f 4400 
  0x0000000112f0e49c: 0048 b8a0 | ab05 0008 

  0x0000000112f0e4a4: ;   {virtual_call}
  0x0000000112f0e4a4: 0000 00e8 

  0x0000000112f0e4a8: ; ImmutableOopMap {[128]=Oop [120]=Oop [144]=Oop [152]=Oop [136]=Oop [112]=Oop [176]=Oop [168]=Oop [184]=Oop }
                      ;*invokevirtual clone {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getExceptionTypes@4 (line 341)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@91 (line 67)
  0x0000000112f0e4a8: 94af 4eff | 4883 f800 | 0f84 2a00 

  0x0000000112f0e4b4: ;   {metadata('java/lang/Class'[])}
  0x0000000112f0e4b4: 0000 48b9 | a0ab 0500 | 0800 0000 | 448b 4008 | 49ba 0000 | 0000 0800 | 0000 4d03 | c249 3b48 
  0x0000000112f0e4d4: 400f 8555 | 0100 00e9 | 0000 0000 

  0x0000000112f0e4e0: ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getExceptionTypes@7 (line 341)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@91 (line 67)
  0x0000000112f0e4e0: 488b f848 | 8b74 2470 | 8b56 1448 

  0x0000000112f0e4ec: ;*getfield method {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@95 (line 67)
  0x0000000112f0e4ec: c1e2 038b 

  0x0000000112f0e4f0: ;*getfield modifiers {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getModifiers@1 (line 241)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@98 (line 68)
  0x0000000112f0e4f0: 5a20 488b | 9424 8800 | 0000 488b | 8c24 9000 | 0000 4c8b | 8424 a800 | 0000 4c8b | 8c24 b000 
  0x0000000112f0e510: 0000 891c | 2448 8bb4 | 2498 0000 

  0x0000000112f0e51c: ;*invokevirtual generateMethod {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@101 (line 63)
                      ;   {optimized virtual_call}
  0x0000000112f0e51c: 0066 90e8 

  0x0000000112f0e520: ; ImmutableOopMap {[128]=Oop [120]=Oop [112]=Oop }
                      ;*invokevirtual generateMethod {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@101 (line 63)
  0x0000000112f0e520: d102 0000 | 4883 f800 | 0f84 2900 

  0x0000000112f0e52c: ;   {metadata('jdk/internal/reflect/MethodAccessorImpl')}
  0x0000000112f0e52c: 0000 48b9 | 88b2 0400 | 0800 0000 | 8b78 0849 | ba00 0000 | 0008 0000 | 0049 03fa | 483b 4f40 
  0x0000000112f0e54c: 0f85 ec00 | 0000 e900 | 0000 0048 

  0x0000000112f0e558: ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@104 (line 63)
  0x0000000112f0e558: 8bf0 488b | 7c24 708b | 5718 48c1 

  0x0000000112f0e564: ;*getfield parent {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@109 (line 69)
  0x0000000112f0e564: e203 410f | be4f 3883 | f900 0f85 | d300 0000 | 4c8b d649 | c1ea 0344 | 8952 0c48 | 8bca 4833 
  0x0000000112f0e584: ce48 c1e9 | 1548 83f9 | 000f 85d8 

  0x0000000112f0e590: ;*putfield delegate {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.DelegatingMethodAccessorImpl::setDelegate@2 (line 47)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@113 (line 69)
  0x0000000112f0e590: 0000 008b | 7714 48c1 

  0x0000000112f0e598: ;*getfield method {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@128 (line 77)
  0x0000000112f0e598: e603 488b | 5424 7848 | 8b8c 2480 

  0x0000000112f0e5a4: ;*invokestatic invoke0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@133 (line 77)
                      ;   {static_call}
  0x0000000112f0e5a4: 0000 00e8 

  0x0000000112f0e5a8: ; ImmutableOopMap {[128]=Oop [120]=Oop [112]=Oop }
                      ;*invokestatic invoke0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@133 (line 77)
  0x0000000112f0e5a8: 34ee ffff | 4881 c4d0 | 0000 005d 

  0x0000000112f0e5b4: ;   {poll_return}
  0x0000000112f0e5b4: 493b a748 | 0300 000f | 87c2 0000 

  0x0000000112f0e5c0: ;*areturn {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@136 (line 77)
  0x0000000112f0e5c0: 00c3 488b | 7424 7049 | 8b87 d803 | 0000 4d33 | d24d 8997 | d803 0000 | 4d33 d24d | 8997 e003 
  0x0000000112f0e5e0: 0000 488b | d8b8 0000 | 0000 8946 | 10f0 8344 

  0x0000000112f0e5f0: ;*putfield generated {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@122 (line 72)
  0x0000000112f0e5f0: 24c0 0048 

  0x0000000112f0e5f4: ; ImmutableOopMap {rcx=Oop rdi=Oop rsi=Oop [112]=Oop rbx=Oop rax=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.reflect.NativeMethodAccessorImpl::invoke@126 (line 73)
                      ;   {section_word}
  0x0000000112f0e5f4: 8bc3 48ba | f6e5 f012 | 0100 0000 

  0x0000000112f0e600: ;   {runtime_call handle_exception_nofpu Runtime1 stub}
  0x0000000112f0e600: e8fb a23e 

  0x0000000112f0e604: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e604: ff90 e815 

  0x0000000112f0e608: ; ImmutableOopMap {rsi=Oop [112]=Oop [128]=Oop [120]=Oop }
                      ;*invokevirtual getDeclaringClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@21 (line 57)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e608: 8b3e ffe8 

  0x0000000112f0e60c: ; ImmutableOopMap {rsi=Oop [112]=Oop [128]=Oop [120]=Oop rdi=Oop }
                      ;*invokevirtual isHidden {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@24 (line 57)
  0x0000000112f0e60c: 108b 3eff 

  0x0000000112f0e610: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x0000000112f0e610: 488b d2e8 

  0x0000000112f0e614: ; ImmutableOopMap {[128]=Oop [120]=Oop rbx=Oop [112]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@52 (line 61)
  0x0000000112f0e614: e893 3eff | e9ac fdff 

  0x0000000112f0e61c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e61c: ffe8 fe8a 

  0x0000000112f0e620: ; ImmutableOopMap {[128]=Oop [120]=Oop rbx=Oop [112]=Oop rax=Oop rdi=Oop }
                      ;*invokevirtual getDeclaringClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@63 (line 63)
  0x0000000112f0e620: 3eff 4889 

  0x0000000112f0e624: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x0000000112f0e624: 0424 e8f5 

  0x0000000112f0e628: ; ImmutableOopMap {[128]=Oop [120]=Oop [144]=Oop [160]=Oop [152]=Oop [112]=Oop [136]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getParameterTypes@7 (line 314)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@77 (line 65)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e628: ae3e ffe8 

  0x0000000112f0e62c: ; ImmutableOopMap {[128]=Oop [120]=Oop [144]=Oop [152]=Oop [136]=Oop rdi=Oop rsi=Oop [112]=Oop rbx=Oop }
                      ;*invokevirtual getReturnType {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@84 (line 66)
  0x0000000112f0e62c: f08a 3eff | 4889 0424 

  0x0000000112f0e634: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x0000000112f0e634: e8e7 ae3e 

  0x0000000112f0e638: ; ImmutableOopMap {[128]=Oop [120]=Oop [144]=Oop [152]=Oop [136]=Oop [112]=Oop [176]=Oop [168]=Oop [184]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.reflect.Method::getExceptionTypes@7 (line 341)
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@91 (line 67)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e638: ffe8 e28a 

  0x0000000112f0e63c: ; ImmutableOopMap {[128]=Oop [120]=Oop [144]=Oop [152]=Oop [136]=Oop [176]=Oop [168]=Oop rdi=Oop rsi=Oop [112]=Oop }
                      ;*invokevirtual getModifiers {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@98 (line 68)
  0x0000000112f0e63c: 3eff 4889 

  0x0000000112f0e640: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x0000000112f0e640: 0424 e8d9 

  0x0000000112f0e644: ; ImmutableOopMap {[128]=Oop [120]=Oop [112]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@104 (line 63)
  0x0000000112f0e644: ae3e ff8b | 4a0c 48c1 | e103 4883 | f900 0f84 | 1cff ffff | 4889 0c24 

  0x0000000112f0e65c: ;   {runtime_call g1_pre_barrier_slow}
  0x0000000112f0e65c: e89f e13e | ffe9 0eff 

  0x0000000112f0e664: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e664: ffff e8b5 

  0x0000000112f0e668: ; ImmutableOopMap {[128]=Oop [120]=Oop rsi=Oop rdi=Oop [112]=Oop rdx=Oop }
                      ;*invokevirtual setDelegate {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@113 (line 69)
  0x0000000112f0e668: 8a3e ff48 | 83fe 000f | 841e ffff | ff48 8914 

  0x0000000112f0e678: ;   {runtime_call g1_post_barrier_slow}
  0x0000000112f0e678: 24e8 82e4 | 3eff e910 

  0x0000000112f0e680: ;   {internal_word}
  0x0000000112f0e680: ffff ff49 | bab4 e5f0 | 1201 0000 | 004d 8997 | 6003 0000 

  0x0000000112f0e694: ;   {runtime_call SafepointBlob}
  0x0000000112f0e694: e9e7 2b34 

  0x0000000112f0e698: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000000112f0e698: ffe8 828a 

  0x0000000112f0e69c: ; ImmutableOopMap {[128]=Oop [120]=Oop rsi=Oop rdi=Oop [112]=Oop rdx=Oop }
                      ;*invokevirtual setDelegate {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.NativeMethodAccessorImpl::invoke@113 (line 69)
  0x0000000112f0e69c: 3eff 488b | 8c24 8000 | 0000 488b | 7c24 78e9 | 12ff ffff | 488b 8c24 | 8000 0000 | 488b 7c24 
  0x0000000112f0e6bc: 78e9 00ff | ffff 488b | 8c24 8000 | 0000 488b | 7c24 78e9 | eefe ffff | 488b 8c24 | 8000 0000 
  0x0000000112f0e6dc: 488b 7c24 | 78e9 dcfe | ffff 488b | 8c24 8000 | 0000 488b | 7c24 78e9 | cafe ffff | 488b 8c24 
  0x0000000112f0e6fc: 8000 0000 | 488b 7c24 | 78e9 b8fe | ffff 488b | 8c24 8000 | 0000 488b | 7c24 78e9 | a6fe ffff 
  0x0000000112f0e71c: 488b 8c24 | 8000 0000 | 488b 7c24 | 78e9 94fe | ffff 488b | 8c24 8000 | 0000 488b | 7c24 78e9 
  0x0000000112f0e73c: 82fe ffff | 488b 8c24 | 8000 0000 | 488b 7c24 | 78e9 70fe | ffff 488b | 8c24 8000 | 0000 488b 
  0x0000000112f0e75c: 7c24 78e9 | 5efe ffff | 488b 8c24 | 8000 0000 | 488b 7c24 | 78e9 4cfe | ffff 9090 | 498b 87d8 
  0x0000000112f0e77c: 0300 0049 | c787 d803 | 0000 0000 | 0000 49c7 | 87e0 0300 | 0000 0000 | 0048 81c4 | d000 0000 
  0x0000000112f0e79c: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000000112f0e79c: 5de9 5e7a | 3eff f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 
  0x0000000112f0e7bc: f4f4 f4f4 
[Stub Code]
  0x0000000112f0e7c0: ;   {no_reloc}
  0x0000000112f0e7c0: 0f1f 4400 

  0x0000000112f0e7c4: ;   {static_stub}
  0x0000000112f0e7c4: 0048 bb00 | 0000 0000 

  0x0000000112f0e7cc: ;   {runtime_call}
  0x0000000112f0e7cc: 0000 00e9 | fbff ffff 

  0x0000000112f0e7d4: ;   {static_stub}
  0x0000000112f0e7d4: 9048 bb00 | 0000 0000 

  0x0000000112f0e7dc: ;   {runtime_call}
  0x0000000112f0e7dc: 0000 00e9 | fbff ffff 

  0x0000000112f0e7e4: ;   {static_stub}
  0x0000000112f0e7e4: 9048 bb00 | 0000 0000 

  0x0000000112f0e7ec: ;   {runtime_call}
  0x0000000112f0e7ec: 0000 00e9 | fbff ffff 

  0x0000000112f0e7f4: ;   {static_stub}
  0x0000000112f0e7f4: 9048 bb20 | 36b8 2e01 

  0x0000000112f0e7fc: ;   {runtime_call I2C/C2I adapters}
  0x0000000112f0e7fc: 0000 00e9 | f67e 41ff 

  0x0000000112f0e804: ;   {static_stub}
  0x0000000112f0e804: 9048 bb00 | 0000 0000 

  0x0000000112f0e80c: ;   {runtime_call}
  0x0000000112f0e80c: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x0000000112f0e814: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000000112f0e814: e8e7 a63e 

  0x0000000112f0e818: ;   {external_word}
  0x0000000112f0e818: ff48 bf73 | 2eb8 0c01 | 0000 0048 

  0x0000000112f0e824: ;   {runtime_call MacroAssembler::debug64(char*, long long, long long*)}
  0x0000000112f0e824: 83e4 f0e8 | a4d0 87f9 

  0x0000000112f0e82c: ;   {section_word}
  0x0000000112f0e82c: f449 ba2d | e8f0 1201 | 0000 0041 

  0x0000000112f0e838: ;   {runtime_call DeoptimizationBlob}
  0x0000000112f0e838: 52e9 6223 | 34ff f4f4 
[/MachCode]


Compiled method (c1)  131926 4531       1       jdk.internal.reflect.DelegatingMethodAccessorImpl::invoke (10 bytes)
 total in heap  [0x0000000112c2c510,0x0000000112c2c858] = 840
 relocation     [0x0000000112c2c670,0x0000000112c2c6a8] = 56
 main code      [0x0000000112c2c6c0,0x0000000112c2c780] = 192
 stub code      [0x0000000112c2c780,0x0000000112c2c7c0] = 64
 metadata       [0x0000000112c2c7c0,0x0000000112c2c7c8] = 8
 scopes data    [0x0000000112c2c7c8,0x0000000112c2c7f0] = 40
 scopes pcs     [0x0000000112c2c7f0,0x0000000112c2c850] = 96
 dependencies   [0x0000000112c2c850,0x0000000112c2c858] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000000012e347460} 'invoke' '(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;' in 'jdk/internal/reflect/DelegatingMethodAccessorImpl'
  # this:     rsi:rsi   = 'jdk/internal/reflect/DelegatingMethodAccessorImpl'
  # parm0:    rdx:rdx   = 'java/lang/Object'
  # parm1:    rcx:rcx   = '[Ljava/lang/Object;'
  #           [sp+0x50]  (sp of caller)
  0x0000000112c2c6c0: 448b 5608 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x0000000112c2c6d4: ;   {runtime_call ic_miss_stub}
  0x0000000112c2c6d4: 0f85 a6e6 | 61ff 660f | 1f44 0000 
[Verified Entry Point]
  0x0000000112c2c6e0: 8984 2400 | c0fe ff55 

  0x0000000112c2c6e8: ;*aload_0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.DelegatingMethodAccessorImpl::invoke@0 (line 43)
  0x0000000112c2c6e8: 4883 ec40 | 8b7e 0c48 

  0x0000000112c2c6f0: ;*getfield delegate {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.DelegatingMethodAccessorImpl::invoke@1 (line 43)
  0x0000000112c2c6f0: c1e7 0348 | 8bda 488b | d348 8bc1 | 488b c848 | 8974 2420 

  0x0000000112c2c704: ;*invokevirtual invoke {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.DelegatingMethodAccessorImpl::invoke@6 (line 43)
  0x0000000112c2c704: 488b f748 | 895c 2430 | 4889 4424 | 280f 1f40 | 0048 b8ff | ffff ffff 

  0x0000000112c2c71c: ;   {virtual_call}
  0x0000000112c2c71c: ffff ffe8 

  0x0000000112c2c720: ; ImmutableOopMap {[32]=Oop [40]=Oop [48]=Oop }
                      ;*invokevirtual invoke {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.reflect.DelegatingMethodAccessorImpl::invoke@6 (line 43)
  0x0000000112c2c720: a44c 6dff | 4883 c440 

  0x0000000112c2c728: ;   {poll_return}
  0x0000000112c2c728: 5d49 3ba7 | 4803 0000 | 0f87 0100 

  0x0000000112c2c734: ;   {internal_word}
  0x0000000112c2c734: 0000 c349 | ba29 c7c2 | 1201 0000 | 004d 8997 | 6003 0000 

  0x0000000112c2c748: ;   {runtime_call SafepointBlob}
  0x0000000112c2c748: e933 4b62 | ff90 9049 | 8b87 d803 | 0000 49c7 | 87d8 0300 | 0000 0000 | 0049 c787 | e003 0000 
  0x0000000112c2c768: 0000 0000 | 4883 c440 

  0x0000000112c2c770: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000000112c2c770: 5de9 8a9a | 6cff f4f4 | f4f4 f4f4 | f4f4 f4f4 
[Stub Code]
  0x0000000112c2c780: ;   {no_reloc}
  0x0000000112c2c780: 0f1f 4400 

  0x0000000112c2c784: ;   {static_stub}
  0x0000000112c2c784: 0048 bb00 | 0000 0000 

  0x0000000112c2c78c: ;   {runtime_call}
  0x0000000112c2c78c: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x0000000112c2c794: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000000112c2c794: e867 c76c 

  0x0000000112c2c798: ;   {external_word}
  0x0000000112c2c798: ff48 bf73 | 2eb8 0c01 | 0000 0048 

  0x0000000112c2c7a4: ;   {runtime_call MacroAssembler::debug64(char*, long long, long long*)}
  0x0000000112c2c7a4: 83e4 f0e8 | 24f1 b5f9 

  0x0000000112c2c7ac: ;   {section_word}
  0x0000000112c2c7ac: f449 baad | c7c2 1201 | 0000 0041 

  0x0000000112c2c7b8: ;   {runtime_call DeoptimizationBlob}
  0x0000000112c2c7b8: 52e9 e243 | 62ff f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00006000046824a0, length=98, elements={
0x00007f9db9158c00, 0x00007f9db915ae00, 0x00007f9da8970c00, 0x00007f9da8971200,
0x00007f9da8972a00, 0x00007f9da8973000, 0x00007f9db9159a00, 0x00007f9da8973600,
0x00007f9db915a000, 0x00007f9db915a600, 0x00007f9da8987600, 0x00007f9db915b400,
0x00007f9d9881bc00, 0x00007f9dd8fff800, 0x00007f9db96e3800, 0x00007f9da8c9da00,
0x00007f9d9aa28000, 0x00007f9d9aa35800, 0x00007f9db9649800, 0x00007f9d9a880e00,
0x00007f9db961c600, 0x00007f9d9b541600, 0x00007f9d9b556c00, 0x00007f9dd8822000,
0x00007f9db95f4400, 0x00007f9da8e27c00, 0x00007f9d9affec00, 0x00007f9db958ba00,
0x00007f9da8c59a00, 0x00007f9db8ce6c00, 0x00007f9d88839e00, 0x00007f9db8d90400,
0x00007f9d88836c00, 0x00007f9d88840a00, 0x00007f9db8d90a00, 0x00007f9db8ce7e00,
0x00007f9d88841000, 0x00007f9db8ce8400, 0x00007f9db8d8ee00, 0x00007f9db8d8f400,
0x00007f9d88841600, 0x00007f9da8c54200, 0x00007f9da8dd7c00, 0x00007f9da8c54800,
0x00007f9d88841c00, 0x00007f9d88843400, 0x00007f9d88842200, 0x00007f9d88842800,
0x00007f9d88842e00, 0x00007f9d88845c00, 0x00007f9db8ffda00, 0x00007f9d9b6af000,
0x00007f9db8ffe600, 0x00007f9db8ffec00, 0x00007f9d9b6af600, 0x00007f9d88846200,
0x00007f9d88844800, 0x00007f9d88843a00, 0x00007f9da8c54e00, 0x00007f9da8ddb400,
0x00007f9da8ddba00, 0x00007f9da8dde200, 0x00007f9d88844000, 0x00007f9da8ddc000,
0x00007f9d88844e00, 0x00007f9d88845400, 0x00007f9da8dda800, 0x00007f9da8ddae00,
0x00007f9da8ddc600, 0x00007f9da8ddcc00, 0x00007f9d8884b600, 0x00007f9db8d16e00,
0x00007f9d8884bc00, 0x00007f9db8d17400, 0x00007f9d9b6afc00, 0x00007f9d8884c200,
0x00007f9db8fff200, 0x00007f9d8884c800, 0x00007f9db8fff800, 0x00007f9d9b6b4c00,
0x00007f9d8884ce00, 0x00007f9da8dde800, 0x00007f9da8ddee00, 0x00007f9da8ddd200,
0x00007f9da8ddd800, 0x00007f9db8d17a00, 0x00007f9d8884d400, 0x00007f9d9b6adc00,
0x00007f9da8de2200, 0x00007f9d8884da00, 0x00007f9d8884e000, 0x00007f9d9b6ae200,
0x00007f9da8de3200, 0x00007f9d9b6b5200, 0x00007f9da8de3800, 0x00007f9d8884b000,
0x00007f9d8883b600, 0x00007f9d9a847a00
}
_to_delete_list=0x0000600004675ac0
Skipping _to_delete_list fields and contents for safety.

Java Threads: ( => current thread )
  0x00007f9db9158c00 JavaThread "Reference Handler" daemon [_thread_blocked, id=22339, stack(0x0000000309d1b000,0x0000000309e1b000)]
  0x00007f9db915ae00 JavaThread "Finalizer" daemon [_thread_blocked, id=23043, stack(0x0000000309e1e000,0x0000000309f1e000)]
  0x00007f9da8970c00 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=29699, stack(0x000000030a02f000,0x000000030a12f000)]
  0x00007f9da8971200 JavaThread "Service Thread" daemon [_thread_blocked, id=25603, stack(0x000000030a132000,0x000000030a232000)]
  0x00007f9da8972a00 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=25859, stack(0x000000030a235000,0x000000030a335000)]
  0x00007f9da8973000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=28931, stack(0x000000030a338000,0x000000030a438000)]
  0x00007f9db9159a00 JavaThread "Sweeper thread" daemon [_thread_blocked, id=26115, stack(0x000000030a43b000,0x000000030a53b000)]
  0x00007f9da8973600 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=26495, stack(0x000000030a53e000,0x000000030a63e000)]
  0x00007f9db915a000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=26959, stack(0x000000030a641000,0x000000030a741000)]
  0x00007f9db915a600 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=32771, stack(0x000000030a744000,0x000000030a844000)]
  0x00007f9da8987600 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=43267, stack(0x000000030a847000,0x000000030a947000)]
  0x00007f9db915b400 JavaThread "Notification Thread" daemon [_thread_blocked, id=34051, stack(0x000000030a94a000,0x000000030aa4a000)]
  0x00007f9d9881bc00 JavaThread "Attach Listener" daemon [_thread_blocked, id=42499, stack(0x000000030aa4d000,0x000000030ab4d000)]
  0x00007f9dd8fff800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=37231, stack(0x000000030b162000,0x000000030b262000)]
  0x00007f9db96e3800 JavaThread "Catalina-utility-1" [_thread_blocked, id=44379, stack(0x000000030b877000,0x000000030b977000)]
  0x00007f9da8c9da00 JavaThread "Catalina-utility-2" [_thread_blocked, id=46091, stack(0x000000030b97a000,0x000000030ba7a000)]
  0x00007f9d9aa28000 JavaThread "container-0" [_thread_blocked, id=63251, stack(0x000000030ba7d000,0x000000030bb7d000)]
  0x00007f9d9aa35800 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=63135, stack(0x000000030bb80000,0x000000030bc80000)]
  0x00007f9db9649800 JavaThread "HikariPool-1 housekeeper" daemon [_thread_blocked, id=62291, stack(0x000000030bc83000,0x000000030bd83000)]
  0x00007f9d9a880e00 JavaThread "HikariPool-2 housekeeper" daemon [_thread_blocked, id=60971, stack(0x000000030be89000,0x000000030bf89000)]
  0x00007f9db961c600 JavaThread "boundedElastic-evictor-1" daemon [_thread_blocked, id=61603, stack(0x000000030bd86000,0x000000030be86000)]
  0x00007f9d9b541600 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=47683, stack(0x000000030bf8c000,0x000000030c08c000)]
  0x00007f9d9b556c00 JavaThread "HttpClient-2-SelectorManager" daemon [_thread_in_native, id=59935, stack(0x000000030c08f000,0x000000030c18f000)]
  0x00007f9dd8822000 JavaThread "RateLimitExecutorDelayThread" daemon [_thread_blocked, id=49375, stack(0x000000030c192000,0x000000030c292000)]
  0x00007f9db95f4400 JavaThread "idle-connection-reaper" daemon [_thread_blocked, id=49459, stack(0x000000030c295000,0x000000030c395000)]
  0x00007f9da8e27c00 JavaThread "HttpClient-3-SelectorManager" daemon [_thread_in_native, id=58603, stack(0x000000030c398000,0x000000030c498000)]
  0x00007f9d9affec00 JavaThread "HttpClient-4-SelectorManager" daemon [_thread_in_native, id=57951, stack(0x000000030c49b000,0x000000030c59b000)]
  0x00007f9db958ba00 JavaThread "HttpClient-5-SelectorManager" daemon [_thread_in_native, id=57691, stack(0x000000030c59e000,0x000000030c69e000)]
  0x00007f9da8c59a00 JavaThread "Telemetry Manager timer" daemon [_thread_blocked, id=49959, stack(0x000000030c6a1000,0x000000030c7a1000)]
  0x00007f9db8ce6c00 JavaThread "Subscription Manager Consumer Thread" daemon [_thread_blocked, id=50331, stack(0x000000030c7a4000,0x000000030c8a4000)]
  0x00007f9d88839e00 JavaThread "File Watcher" daemon [_thread_blocked, id=51471, stack(0x000000030c8a7000,0x000000030c9a7000)]
  0x00007f9db8d90400 JavaThread "Live Reload Server" daemon [_thread_in_native, id=56327, stack(0x000000030c9aa000,0x000000030caaa000)]
  0x00007f9d88836c00 JavaThread "http-nio-0.0.0.0-9000-exec-1" daemon [_thread_blocked, id=52503, stack(0x000000030caad000,0x000000030cbad000)]
  0x00007f9d88840a00 JavaThread "http-nio-0.0.0.0-9000-exec-2" daemon [_thread_blocked, id=52739, stack(0x000000030cbb0000,0x000000030ccb0000)]
  0x00007f9db8d90a00 JavaThread "http-nio-0.0.0.0-9000-exec-3" daemon [_thread_blocked, id=47879, stack(0x000000030ccb3000,0x000000030cdb3000)]
  0x00007f9db8ce7e00 JavaThread "http-nio-0.0.0.0-9000-exec-4" daemon [_thread_blocked, id=61879, stack(0x000000030cdb6000,0x000000030ceb6000)]
  0x00007f9d88841000 JavaThread "http-nio-0.0.0.0-9000-exec-5" daemon [_thread_blocked, id=52995, stack(0x000000030ceb9000,0x000000030cfb9000)]
  0x00007f9db8ce8400 JavaThread "http-nio-0.0.0.0-9000-exec-6" daemon [_thread_blocked, id=55043, stack(0x000000030cfbc000,0x000000030d0bc000)]
  0x00007f9db8d8ee00 JavaThread "http-nio-0.0.0.0-9000-exec-7" daemon [_thread_blocked, id=54531, stack(0x000000030d0bf000,0x000000030d1bf000)]
  0x00007f9db8d8f400 JavaThread "http-nio-0.0.0.0-9000-exec-8" daemon [_thread_blocked, id=54275, stack(0x000000030d1c2000,0x000000030d2c2000)]
  0x00007f9d88841600 JavaThread "http-nio-0.0.0.0-9000-exec-9" daemon [_thread_blocked, id=53507, stack(0x000000030d2c5000,0x000000030d3c5000)]
  0x00007f9da8c54200 JavaThread "http-nio-0.0.0.0-9000-exec-10" daemon [_thread_blocked, id=65539, stack(0x000000030d3c8000,0x000000030d4c8000)]
  0x00007f9da8dd7c00 JavaThread "http-nio-0.0.0.0-9000-Poller" daemon [_thread_in_native, id=87043, stack(0x000000030d4cb000,0x000000030d5cb000)]
  0x00007f9da8c54800 JavaThread "http-nio-0.0.0.0-9000-Acceptor" daemon [_thread_in_native, id=86531, stack(0x000000030d5ce000,0x000000030d6ce000)]
  0x00007f9d88841c00 JavaThread "AMQP Connection 127.0.0.1:5672" [_thread_in_native, id=66731, stack(0x000000030d6d1000,0x000000030d7d1000)]
  0x00007f9d88843400 JavaThread "pool-2-thread-2" [_thread_blocked, id=66847, stack(0x000000030d7d4000,0x000000030d8d4000)]
  0x00007f9d88842200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1" [_thread_blocked, id=85315, stack(0x000000030d8d7000,0x000000030d9d7000)]
  0x00007f9d88842800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2" [_thread_blocked, id=67331, stack(0x000000030d9da000,0x000000030dada000)]
  0x00007f9d88842e00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3" [_thread_blocked, id=67587, stack(0x000000030dadd000,0x000000030dbdd000)]
  0x00007f9d88845c00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4" [_thread_blocked, id=84227, stack(0x000000030dbe0000,0x000000030dce0000)]
  0x00007f9db8ffda00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5" [_thread_blocked, id=83715, stack(0x000000030dce3000,0x000000030dde3000)]
  0x00007f9d9b6af000 JavaThread "pool-2-thread-3" [_thread_blocked, id=68355, stack(0x000000030dde6000,0x000000030dee6000)]
  0x00007f9db8ffe600 JavaThread "pool-2-thread-4" [_thread_blocked, id=82691, stack(0x000000030dee9000,0x000000030dfe9000)]
  0x00007f9db8ffec00 JavaThread "pool-2-thread-5" [_thread_blocked, id=82439, stack(0x000000030dfec000,0x000000030e0ec000)]
  0x00007f9d9b6af600 JavaThread "pool-2-thread-6" [_thread_blocked, id=69127, stack(0x000000030e0ef000,0x000000030e1ef000)]
  0x00007f9d88846200 JavaThread "pool-2-thread-7" [_thread_blocked, id=69903, stack(0x000000030e1f2000,0x000000030e2f2000)]
  0x00007f9d88844800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1" [_thread_blocked, id=70659, stack(0x000000030e2f5000,0x000000030e3f5000)]
  0x00007f9d88843a00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2" [_thread_blocked, id=70915, stack(0x000000030e3f8000,0x000000030e4f8000)]
  0x00007f9da8c54e00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3" [_thread_blocked, id=81155, stack(0x000000030e4fb000,0x000000030e5fb000)]
  0x00007f9da8ddb400 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-4" [_thread_blocked, id=80643, stack(0x000000030e5fe000,0x000000030e6fe000)]
  0x00007f9da8ddba00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-5" [_thread_blocked, id=80131, stack(0x000000030e701000,0x000000030e801000)]
  0x00007f9da8dde200 JavaThread "pool-2-thread-8" [_thread_blocked, id=71683, stack(0x000000030e804000,0x000000030e904000)]
  0x00007f9d88844000 JavaThread "pool-2-thread-9" [_thread_blocked, id=79363, stack(0x000000030e907000,0x000000030ea07000)]
  0x00007f9da8ddc000 JavaThread "pool-2-thread-10" [_thread_blocked, id=72451, stack(0x000000030ea0a000,0x000000030eb0a000)]
  0x00007f9d88844e00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1" [_thread_blocked, id=73735, stack(0x000000030eb0d000,0x000000030ec0d000)]
  0x00007f9d88845400 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2" [_thread_blocked, id=74243, stack(0x000000030ec10000,0x000000030ed10000)]
  0x00007f9da8dda800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3" [_thread_blocked, id=74755, stack(0x000000030ed13000,0x000000030ee13000)]
  0x00007f9da8ddae00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-4" [_thread_blocked, id=78595, stack(0x000000030ee16000,0x000000030ef16000)]
  0x00007f9da8ddc600 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-5" [_thread_blocked, id=78083, stack(0x000000030ef19000,0x000000030f019000)]
  0x00007f9da8ddcc00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1" [_thread_blocked, id=76547, stack(0x000000030f01c000,0x000000030f11c000)]
  0x00007f9d8884b600 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2" [_thread_blocked, id=77315, stack(0x000000030f11f000,0x000000030f21f000)]
  0x00007f9db8d16e00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3" [_thread_blocked, id=76803, stack(0x000000030f222000,0x000000030f322000)]
  0x00007f9d8884bc00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4" [_thread_blocked, id=130819, stack(0x000000030f325000,0x000000030f425000)]
  0x00007f9db8d17400 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5" [_thread_blocked, id=130307, stack(0x000000030f428000,0x000000030f528000)]
  0x00007f9d9b6afc00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-1" [_thread_blocked, id=129319, stack(0x000000030f52b000,0x000000030f62b000)]
  0x00007f9d8884c200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-2" [_thread_blocked, id=88323, stack(0x000000030f62e000,0x000000030f72e000)]
  0x00007f9db8fff200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-3" [_thread_blocked, id=88839, stack(0x000000030f731000,0x000000030f831000)]
  0x00007f9d8884c800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-4" [_thread_blocked, id=128515, stack(0x000000030f834000,0x000000030f934000)]
  0x00007f9db8fff800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#4-5" [_thread_blocked, id=89091, stack(0x000000030f937000,0x000000030fa37000)]
  0x00007f9d9b6b4c00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#5-1" [_thread_blocked, id=89607, stack(0x000000030fa3a000,0x000000030fb3a000)]
  0x00007f9d8884ce00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-1" [_thread_blocked, id=89859, stack(0x000000030fb3d000,0x000000030fc3d000)]
  0x00007f9da8dde800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-2" [_thread_blocked, id=90375, stack(0x000000030fc40000,0x000000030fd40000)]
  0x00007f9da8ddee00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-3" [_thread_blocked, id=90883, stack(0x000000030fd43000,0x000000030fe43000)]
  0x00007f9da8ddd200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-4" [_thread_blocked, id=91139, stack(0x000000030fe46000,0x000000030ff46000)]
  0x00007f9da8ddd800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#6-5" [_thread_blocked, id=125699, stack(0x000000030ff49000,0x0000000310049000)]
  0x00007f9db8d17a00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-1" [_thread_blocked, id=92163, stack(0x000000031004c000,0x000000031014c000)]
  0x00007f9d8884d400 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-2" [_thread_blocked, id=92419, stack(0x000000031014f000,0x000000031024f000)]
  0x00007f9d9b6adc00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-3" [_thread_blocked, id=123907, stack(0x0000000310252000,0x0000000310352000)]
  0x00007f9da8de2200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-4" [_thread_blocked, id=93187, stack(0x0000000310355000,0x0000000310455000)]
  0x00007f9d8884da00 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-5" [_thread_blocked, id=123655, stack(0x0000000310458000,0x0000000310558000)]
  0x00007f9d8884e000 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-1" [_thread_blocked, id=123139, stack(0x000000031055b000,0x000000031065b000)]
  0x00007f9d9b6ae200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-2" [_thread_blocked, id=122627, stack(0x000000031065e000,0x000000031075e000)]
  0x00007f9da8de3200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-3" [_thread_blocked, id=94723, stack(0x0000000310761000,0x0000000310861000)]
  0x00007f9d9b6b5200 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-4" [_thread_blocked, id=121859, stack(0x0000000310864000,0x0000000310964000)]
  0x00007f9da8de3800 JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#8-5" [_thread_blocked, id=121603, stack(0x0000000310967000,0x0000000310a67000)]
=>0x00007f9d8884b000 JavaThread "scheduling-1" [_thread_in_Java, id=97043, stack(0x0000000310c70000,0x0000000310d70000)]
  0x00007f9d8883b600 JavaThread "DestroyJavaVM" [_thread_blocked, id=9219, stack(0x0000000309606000,0x0000000309706000)]
  0x00007f9d9a847a00 JavaThread "Async-profiler Timer" daemon [_thread_in_native, id=119559, stack(0x000000030b46b000,0x000000030b56b000)]

Other Threads:
  0x00007f9dd9957350 VMThread "VM Thread" [stack: 0x0000000309c18000,0x0000000309d18000] [id=32515] _threads_hazard_ptr=0x00006000046824a0
  0x00007f9db98fa030 WatcherThread [stack: 0x000000030b265000,0x000000030b365000] [id=38659]
  0x00007f9d991918e0 GCTaskThread "GC Thread#0" [stack: 0x0000000309709000,0x0000000309809000] [id=16643]
  0x00007f9dd8793aa0 GCTaskThread "GC Thread#1" [stack: 0x000000030ab50000,0x000000030ac50000] [id=41739]
  0x00007f9dd8794140 GCTaskThread "GC Thread#2" [stack: 0x000000030ac53000,0x000000030ad53000] [id=41219]
  0x00007f9dd996a500 GCTaskThread "GC Thread#3" [stack: 0x000000030ad56000,0x000000030ae56000] [id=35331]
  0x00007f9dd996aba0 GCTaskThread "GC Thread#4" [stack: 0x000000030ae59000,0x000000030af59000] [id=40451]
  0x00007f9dd87949b0 GCTaskThread "GC Thread#5" [stack: 0x000000030af5c000,0x000000030b05c000] [id=35587]
  0x00007f9dd996b680 GCTaskThread "GC Thread#6" [stack: 0x000000030b05f000,0x000000030b15f000] [id=36667]
  0x00007f9db98fee80 GCTaskThread "GC Thread#7" [stack: 0x000000030b368000,0x000000030b468000] [id=38187]
  0x00007f9dd8771920 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000030980c000,0x000000030990c000] [id=21507]
  0x00007f9dd87721b0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000030990f000,0x0000000309a0f000] [id=17411]
  0x00007f9d991c6e40 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000030b774000,0x000000030b874000] [id=45483]
  0x00007f9dd8774a20 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000000309a12000,0x0000000309b12000] [id=17667]
  0x00007f9dd99f7bd0 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000030b671000,0x000000030b771000] [id=63747]
  0x00007f9d88760cc0 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000000310a6a000,0x0000000310b6a000] [id=96259]
  0x00007f9d88762090 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000000310b6d000,0x0000000310c6d000] [id=120835]
  0x00007f9d99191ea0 ConcurrentGCThread "G1 Service" [stack: 0x0000000309b15000,0x0000000309c15000] [id=19971]

Threads with active compile tasks:

VM state: synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00006000020c5380] Threads_lock - owner thread: 0x00007f9dd9957350

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 8 total, 8 available
 Memory: 16384M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 749568K, used 410393K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 132 young (270336K), 22 survivors (45056K)
 Metaspace       used 159605K, committed 160448K, reserved 1245184K
  class space    used 21254K, committed 21632K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%| O|  |TAMS 0x0000000700200000, 0x0000000700200000| Untracked 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%| O|  |TAMS 0x0000000700400000, 0x0000000700400000| Untracked 
|   2|0x0000000700400000, 0x0000000700400000, 0x0000000700600000|  0%| F|  |TAMS 0x0000000700400000, 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%| O|  |TAMS 0x0000000700800000, 0x0000000700800000| Untracked 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700a00000| Untracked 
|   5|0x0000000700a00000, 0x0000000700c00000, 0x0000000700c00000|100%| O|  |TAMS 0x0000000700c00000, 0x0000000700c00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%| O|  |TAMS 0x0000000700e00000, 0x0000000700e00000| Untracked 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%| O|  |TAMS 0x0000000701000000, 0x0000000701000000| Untracked 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%| O|  |TAMS 0x0000000701200000, 0x0000000701200000| Untracked 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%| O|  |TAMS 0x0000000701400000, 0x0000000701400000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701600000, 0x0000000701600000| Untracked 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%| O|  |TAMS 0x0000000701800000, 0x0000000701800000| Untracked 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701a00000, 0x0000000701a00000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701c00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701e00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000702000000, 0x0000000702000000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702200000, 0x0000000702200000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702400000, 0x0000000702400000| Untracked 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702600000| Untracked 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000, 0x0000000702800000| Untracked 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000, 0x0000000702a00000| Untracked 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Untracked 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%| O|  |TAMS 0x0000000703000000, 0x0000000703000000| Untracked 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| O|  |TAMS 0x0000000703400000, 0x0000000703400000| Untracked 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000, 0x0000000703600000| Untracked 
|  27|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703800000| Untracked 
|  28|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|  29|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703c00000, 0x0000000703c00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  31|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|  32|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|  33|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|  34|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  35|0x0000000704600000, 0x0000000704800000, 0x0000000704800000|100%| O|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  36|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  37|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704fc4800, 0x0000000705000000| 88%| O|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  41|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  42|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  43|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  44|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  45|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  47|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000706000000, 0x0000000706000000| Untracked 
|  48|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706200000, 0x0000000706200000| Untracked 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  50|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  51|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  52|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  53|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706d6a000, 0x0000000706e00000| Untracked 
|  55|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000706e00000, 0x0000000707000000| Untracked 
|  56|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707000000, 0x0000000707200000| Untracked 
|  57|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707200000, 0x0000000707400000| Untracked 
|  58|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707400000, 0x0000000707600000| Untracked 
|  59|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707600000, 0x0000000707800000| Untracked 
|  60|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000, 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000, 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000, 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%|HS|  |TAMS 0x0000000707e00000, 0x0000000708000000| Complete 
|  64|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000, 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000, 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%|HS|  |TAMS 0x0000000708400000, 0x0000000708600000| Complete 
|  67|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%|HC|  |TAMS 0x0000000708600000, 0x0000000708800000| Complete 
|  68|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000, 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000, 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%|HS|  |TAMS 0x0000000708c00000, 0x0000000708e00000| Complete 
|  71|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%|HC|  |TAMS 0x0000000708e00000, 0x0000000709000000| Complete 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000, 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709400000, 0x0000000709600000| Untracked 
|  75|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709600000, 0x0000000709800000| Untracked 
|  76|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709800000, 0x0000000709a00000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000, 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000, 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000, 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000, 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000, 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| O|  |TAMS 0x000000070a400000, 0x000000070a600000| Untracked 
|  83|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a600000, 0x000000070a800000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000, 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|  |TAMS 0x000000070aa00000, 0x000000070ac00000| Untracked 
|  86|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ac00000, 0x000000070adcca00| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000, 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000, 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000, 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000, 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000, 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000, 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000, 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000, 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000, 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000, 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000, 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000, 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000, 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000, 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000, 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000, 0x000000070e600000| Untracked 
| 116|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000, 0x000000070e800000| Untracked 
| 117|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000, 0x000000070ea00000| Untracked 
| 118|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000, 0x000000070ec00000| Untracked 
| 119|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000, 0x000000070ee00000| Untracked 
| 120|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000, 0x000000070f000000| Untracked 
| 121|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000, 0x000000070f200000| Untracked 
| 122|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000, 0x000000070f400000| Untracked 
| 123|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000, 0x000000070f600000| Untracked 
| 124|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000, 0x000000070f800000| Untracked 
| 125|0x000000070fa00000, 0x000000070fb01db8, 0x000000070fc00000| 50%| S|CS|TAMS 0x000000070fa00000, 0x000000070fa00000| Complete 
| 126|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| S|CS|TAMS 0x000000070fc00000, 0x000000070fc00000| Complete 
| 127|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| S|CS|TAMS 0x000000070fe00000, 0x000000070fe00000| Complete 
| 128|0x0000000710000000, 0x0000000710200000, 0x0000000710200000|100%| S|CS|TAMS 0x0000000710000000, 0x0000000710000000| Complete 
| 129|0x0000000710200000, 0x0000000710400000, 0x0000000710400000|100%| S|CS|TAMS 0x0000000710200000, 0x0000000710200000| Complete 
| 130|0x0000000710400000, 0x0000000710600000, 0x0000000710600000|100%| S|CS|TAMS 0x0000000710400000, 0x0000000710400000| Complete 
| 131|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000, 0x0000000710600000| Untracked 
| 132|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000, 0x0000000710800000| Untracked 
| 133|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000, 0x0000000710a00000| Untracked 
| 134|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000, 0x0000000710c00000| Untracked 
| 135|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000, 0x0000000710e00000| Untracked 
| 136|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000, 0x0000000711000000| Untracked 
| 137|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%| S|CS|TAMS 0x0000000711200000, 0x0000000711200000| Complete 
| 138|0x0000000711400000, 0x0000000711600000, 0x0000000711600000|100%| S|CS|TAMS 0x0000000711400000, 0x0000000711400000| Complete 
| 139|0x0000000711600000, 0x0000000711800000, 0x0000000711800000|100%| S|CS|TAMS 0x0000000711600000, 0x0000000711600000| Complete 
| 140|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| S|CS|TAMS 0x0000000711800000, 0x0000000711800000| Complete 
| 141|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| S|CS|TAMS 0x0000000711a00000, 0x0000000711a00000| Complete 
| 142|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| S|CS|TAMS 0x0000000711c00000, 0x0000000711c00000| Complete 
| 143|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| S|CS|TAMS 0x0000000711e00000, 0x0000000711e00000| Complete 
| 144|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| S|CS|TAMS 0x0000000712000000, 0x0000000712000000| Complete 
| 145|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| S|CS|TAMS 0x0000000712200000, 0x0000000712200000| Complete 
| 146|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| S|CS|TAMS 0x0000000712400000, 0x0000000712400000| Complete 
| 147|0x0000000712600000, 0x0000000712600000, 0x0000000712800000|  0%| F|  |TAMS 0x0000000712600000, 0x0000000712600000| Untracked 
| 148|0x0000000712800000, 0x0000000712800000, 0x0000000712a00000|  0%| F|  |TAMS 0x0000000712800000, 0x0000000712800000| Untracked 
| 149|0x0000000712a00000, 0x0000000712a00000, 0x0000000712c00000|  0%| F|  |TAMS 0x0000000712a00000, 0x0000000712a00000| Untracked 
| 150|0x0000000712c00000, 0x0000000712c00000, 0x0000000712e00000|  0%| F|  |TAMS 0x0000000712c00000, 0x0000000712c00000| Untracked 
| 151|0x0000000712e00000, 0x0000000712e00000, 0x0000000713000000|  0%| F|  |TAMS 0x0000000712e00000, 0x0000000712e00000| Untracked 
| 152|0x0000000713000000, 0x0000000713000000, 0x0000000713200000|  0%| F|  |TAMS 0x0000000713000000, 0x0000000713000000| Untracked 
| 153|0x0000000713200000, 0x0000000713200000, 0x0000000713400000|  0%| F|  |TAMS 0x0000000713200000, 0x0000000713200000| Untracked 
| 154|0x0000000713400000, 0x0000000713400000, 0x0000000713600000|  0%| F|  |TAMS 0x0000000713400000, 0x0000000713400000| Untracked 
| 155|0x0000000713600000, 0x0000000713600000, 0x0000000713800000|  0%| F|  |TAMS 0x0000000713600000, 0x0000000713600000| Untracked 
| 156|0x0000000713800000, 0x0000000713800000, 0x0000000713a00000|  0%| F|  |TAMS 0x0000000713800000, 0x0000000713800000| Untracked 
| 157|0x0000000713a00000, 0x0000000713c00000, 0x0000000713c00000|100%| S|CS|TAMS 0x0000000713a00000, 0x0000000713a00000| Complete 
| 158|0x0000000713c00000, 0x0000000713e00000, 0x0000000713e00000|100%| S|CS|TAMS 0x0000000713c00000, 0x0000000713c00000| Complete 
| 159|0x0000000713e00000, 0x0000000714000000, 0x0000000714000000|100%| S|CS|TAMS 0x0000000713e00000, 0x0000000713e00000| Complete 
| 160|0x0000000714000000, 0x0000000714200000, 0x0000000714200000|100%| S|CS|TAMS 0x0000000714000000, 0x0000000714000000| Complete 
| 161|0x0000000714200000, 0x0000000714400000, 0x0000000714400000|100%| S|CS|TAMS 0x0000000714200000, 0x0000000714200000| Complete 
| 162|0x0000000714400000, 0x0000000714600000, 0x0000000714600000|100%| S|CS|TAMS 0x0000000714400000, 0x0000000714400000| Complete 
| 163|0x0000000714600000, 0x0000000714600000, 0x0000000714800000|  0%| F|  |TAMS 0x0000000714600000, 0x0000000714600000| Untracked 
| 164|0x0000000714800000, 0x0000000714800000, 0x0000000714a00000|  0%| F|  |TAMS 0x0000000714800000, 0x0000000714800000| Untracked 
| 165|0x0000000714a00000, 0x0000000714a00000, 0x0000000714c00000|  0%| F|  |TAMS 0x0000000714a00000, 0x0000000714a00000| Untracked 
| 166|0x0000000714c00000, 0x0000000714c00000, 0x0000000714e00000|  0%| F|  |TAMS 0x0000000714c00000, 0x0000000714c00000| Untracked 
| 167|0x0000000714e00000, 0x0000000714e00000, 0x0000000715000000|  0%| F|  |TAMS 0x0000000714e00000, 0x0000000714e00000| Untracked 
| 168|0x0000000715000000, 0x0000000715000000, 0x0000000715200000|  0%| F|  |TAMS 0x0000000715000000, 0x0000000715000000| Untracked 
| 169|0x0000000715200000, 0x0000000715200000, 0x0000000715400000|  0%| F|  |TAMS 0x0000000715200000, 0x0000000715200000| Untracked 
| 170|0x0000000715400000, 0x0000000715400000, 0x0000000715600000|  0%| F|  |TAMS 0x0000000715400000, 0x0000000715400000| Untracked 
| 171|0x0000000715600000, 0x0000000715600000, 0x0000000715800000|  0%| F|  |TAMS 0x0000000715600000, 0x0000000715600000| Untracked 
| 172|0x0000000715800000, 0x0000000715800000, 0x0000000715a00000|  0%| F|  |TAMS 0x0000000715800000, 0x0000000715800000| Untracked 
| 173|0x0000000715a00000, 0x0000000715a00000, 0x0000000715c00000|  0%| F|  |TAMS 0x0000000715a00000, 0x0000000715a00000| Untracked 
| 174|0x0000000715c00000, 0x0000000715c00000, 0x0000000715e00000|  0%| F|  |TAMS 0x0000000715c00000, 0x0000000715c00000| Untracked 
| 175|0x0000000715e00000, 0x0000000715e00000, 0x0000000716000000|  0%| F|  |TAMS 0x0000000715e00000, 0x0000000715e00000| Untracked 
| 176|0x0000000716000000, 0x0000000716000000, 0x0000000716200000|  0%| F|  |TAMS 0x0000000716000000, 0x0000000716000000| Untracked 
| 177|0x0000000716200000, 0x0000000716200000, 0x0000000716400000|  0%| F|  |TAMS 0x0000000716200000, 0x0000000716200000| Untracked 
| 178|0x0000000716400000, 0x0000000716400000, 0x0000000716600000|  0%| F|  |TAMS 0x0000000716400000, 0x0000000716400000| Untracked 
| 179|0x0000000716600000, 0x0000000716600000, 0x0000000716800000|  0%| F|  |TAMS 0x0000000716600000, 0x0000000716600000| Untracked 
| 180|0x0000000716800000, 0x0000000716800000, 0x0000000716a00000|  0%| F|  |TAMS 0x0000000716800000, 0x0000000716800000| Untracked 
| 181|0x0000000716a00000, 0x0000000716a00000, 0x0000000716c00000|  0%| F|  |TAMS 0x0000000716a00000, 0x0000000716a00000| Untracked 
| 182|0x0000000716c00000, 0x0000000716c00000, 0x0000000716e00000|  0%| F|  |TAMS 0x0000000716c00000, 0x0000000716c00000| Untracked 
| 183|0x0000000716e00000, 0x0000000716e00000, 0x0000000717000000|  0%| F|  |TAMS 0x0000000716e00000, 0x0000000716e00000| Untracked 
| 184|0x0000000717000000, 0x0000000717000000, 0x0000000717200000|  0%| F|  |TAMS 0x0000000717000000, 0x0000000717000000| Untracked 
| 185|0x0000000717200000, 0x0000000717200000, 0x0000000717400000|  0%| F|  |TAMS 0x0000000717200000, 0x0000000717200000| Untracked 
| 186|0x0000000717400000, 0x0000000717400000, 0x0000000717600000|  0%| F|  |TAMS 0x0000000717400000, 0x0000000717400000| Untracked 
| 187|0x0000000717600000, 0x0000000717600000, 0x0000000717800000|  0%| F|  |TAMS 0x0000000717600000, 0x0000000717600000| Untracked 
| 188|0x0000000717800000, 0x0000000717800000, 0x0000000717a00000|  0%| F|  |TAMS 0x0000000717800000, 0x0000000717800000| Untracked 
| 189|0x0000000717a00000, 0x0000000717a00000, 0x0000000717c00000|  0%| F|  |TAMS 0x0000000717a00000, 0x0000000717a00000| Untracked 
| 190|0x0000000717c00000, 0x0000000717c00000, 0x0000000717e00000|  0%| F|  |TAMS 0x0000000717c00000, 0x0000000717c00000| Untracked 
| 191|0x0000000717e00000, 0x0000000717e00000, 0x0000000718000000|  0%| F|  |TAMS 0x0000000717e00000, 0x0000000717e00000| Untracked 
| 192|0x0000000718000000, 0x0000000718000000, 0x0000000718200000|  0%| F|  |TAMS 0x0000000718000000, 0x0000000718000000| Untracked 
| 193|0x0000000718200000, 0x0000000718200000, 0x0000000718400000|  0%| F|  |TAMS 0x0000000718200000, 0x0000000718200000| Untracked 
| 194|0x0000000718400000, 0x0000000718400000, 0x0000000718600000|  0%| F|  |TAMS 0x0000000718400000, 0x0000000718400000| Untracked 
| 195|0x0000000718600000, 0x0000000718600000, 0x0000000718800000|  0%| F|  |TAMS 0x0000000718600000, 0x0000000718600000| Untracked 
| 196|0x0000000718800000, 0x0000000718800000, 0x0000000718a00000|  0%| F|  |TAMS 0x0000000718800000, 0x0000000718800000| Untracked 
| 197|0x0000000718a00000, 0x0000000718a00000, 0x0000000718c00000|  0%| F|  |TAMS 0x0000000718a00000, 0x0000000718a00000| Untracked 
| 198|0x0000000718c00000, 0x0000000718c00000, 0x0000000718e00000|  0%| F|  |TAMS 0x0000000718c00000, 0x0000000718c00000| Untracked 
| 199|0x0000000718e00000, 0x0000000718e00000, 0x0000000719000000|  0%| F|  |TAMS 0x0000000718e00000, 0x0000000718e00000| Untracked 
| 200|0x0000000719000000, 0x0000000719000000, 0x0000000719200000|  0%| F|  |TAMS 0x0000000719000000, 0x0000000719000000| Untracked 
| 201|0x0000000719200000, 0x0000000719200000, 0x0000000719400000|  0%| F|  |TAMS 0x0000000719200000, 0x0000000719200000| Untracked 
| 202|0x0000000719400000, 0x0000000719400000, 0x0000000719600000|  0%| F|  |TAMS 0x0000000719400000, 0x0000000719400000| Untracked 
| 203|0x0000000719600000, 0x0000000719600000, 0x0000000719800000|  0%| F|  |TAMS 0x0000000719600000, 0x0000000719600000| Untracked 
| 204|0x0000000719800000, 0x0000000719800000, 0x0000000719a00000|  0%| F|  |TAMS 0x0000000719800000, 0x0000000719800000| Untracked 
| 205|0x0000000719a00000, 0x0000000719a00000, 0x0000000719c00000|  0%| F|  |TAMS 0x0000000719a00000, 0x0000000719a00000| Untracked 
| 206|0x0000000719c00000, 0x0000000719c00000, 0x0000000719e00000|  0%| F|  |TAMS 0x0000000719c00000, 0x0000000719c00000| Untracked 
| 207|0x0000000719e00000, 0x0000000719e00000, 0x000000071a000000|  0%| F|  |TAMS 0x0000000719e00000, 0x0000000719e00000| Untracked 
| 208|0x000000071a000000, 0x000000071a000000, 0x000000071a200000|  0%| F|  |TAMS 0x000000071a000000, 0x000000071a000000| Untracked 
| 209|0x000000071a200000, 0x000000071a200000, 0x000000071a400000|  0%| F|  |TAMS 0x000000071a200000, 0x000000071a200000| Untracked 
| 210|0x000000071a400000, 0x000000071a400000, 0x000000071a600000|  0%| F|  |TAMS 0x000000071a400000, 0x000000071a400000| Untracked 
| 211|0x000000071a600000, 0x000000071a600000, 0x000000071a800000|  0%| F|  |TAMS 0x000000071a600000, 0x000000071a600000| Untracked 
| 212|0x000000071a800000, 0x000000071a800000, 0x000000071aa00000|  0%| F|  |TAMS 0x000000071a800000, 0x000000071a800000| Untracked 
| 213|0x000000071aa00000, 0x000000071aa00000, 0x000000071ac00000|  0%| F|  |TAMS 0x000000071aa00000, 0x000000071aa00000| Untracked 
| 214|0x000000071ac00000, 0x000000071ac00000, 0x000000071ae00000|  0%| F|  |TAMS 0x000000071ac00000, 0x000000071ac00000| Untracked 
| 215|0x000000071ae00000, 0x000000071ae00000, 0x000000071b000000|  0%| F|  |TAMS 0x000000071ae00000, 0x000000071ae00000| Untracked 
| 216|0x000000071b000000, 0x000000071b000000, 0x000000071b200000|  0%| F|  |TAMS 0x000000071b000000, 0x000000071b000000| Untracked 
| 217|0x000000071b200000, 0x000000071b200000, 0x000000071b400000|  0%| F|  |TAMS 0x000000071b200000, 0x000000071b200000| Untracked 
| 218|0x000000071b400000, 0x000000071b400000, 0x000000071b600000|  0%| F|  |TAMS 0x000000071b400000, 0x000000071b400000| Untracked 
| 219|0x000000071b600000, 0x000000071b600000, 0x000000071b800000|  0%| F|  |TAMS 0x000000071b600000, 0x000000071b600000| Untracked 
| 220|0x000000071b800000, 0x000000071b800000, 0x000000071ba00000|  0%| F|  |TAMS 0x000000071b800000, 0x000000071b800000| Untracked 
| 221|0x000000071ba00000, 0x000000071ba00000, 0x000000071bc00000|  0%| F|  |TAMS 0x000000071ba00000, 0x000000071ba00000| Untracked 
| 222|0x000000071bc00000, 0x000000071bc00000, 0x000000071be00000|  0%| F|  |TAMS 0x000000071bc00000, 0x000000071bc00000| Untracked 
| 223|0x000000071be00000, 0x000000071be00000, 0x000000071c000000|  0%| F|  |TAMS 0x000000071be00000, 0x000000071be00000| Untracked 
| 224|0x000000071c000000, 0x000000071c000000, 0x000000071c200000|  0%| F|  |TAMS 0x000000071c000000, 0x000000071c000000| Untracked 
| 225|0x000000071c200000, 0x000000071c200000, 0x000000071c400000|  0%| F|  |TAMS 0x000000071c200000, 0x000000071c200000| Untracked 
| 226|0x000000071c400000, 0x000000071c400000, 0x000000071c600000|  0%| F|  |TAMS 0x000000071c400000, 0x000000071c400000| Untracked 
| 227|0x000000071c600000, 0x000000071c600000, 0x000000071c800000|  0%| F|  |TAMS 0x000000071c600000, 0x000000071c600000| Untracked 
| 228|0x000000071c800000, 0x000000071c800000, 0x000000071ca00000|  0%| F|  |TAMS 0x000000071c800000, 0x000000071c800000| Untracked 
| 229|0x000000071ca00000, 0x000000071ca00000, 0x000000071cc00000|  0%| F|  |TAMS 0x000000071ca00000, 0x000000071ca00000| Untracked 
| 230|0x000000071cc00000, 0x000000071cc00000, 0x000000071ce00000|  0%| F|  |TAMS 0x000000071cc00000, 0x000000071cc00000| Untracked 
| 231|0x000000071ce00000, 0x000000071ce00000, 0x000000071d000000|  0%| F|  |TAMS 0x000000071ce00000, 0x000000071ce00000| Untracked 
| 232|0x000000071d000000, 0x000000071d000000, 0x000000071d200000|  0%| F|  |TAMS 0x000000071d000000, 0x000000071d000000| Untracked 
| 233|0x000000071d200000, 0x000000071d200000, 0x000000071d400000|  0%| F|  |TAMS 0x000000071d200000, 0x000000071d200000| Untracked 
| 234|0x000000071d400000, 0x000000071d400000, 0x000000071d600000|  0%| F|  |TAMS 0x000000071d400000, 0x000000071d400000| Untracked 
| 235|0x000000071d600000, 0x000000071d600000, 0x000000071d800000|  0%| F|  |TAMS 0x000000071d600000, 0x000000071d600000| Untracked 
| 236|0x000000071d800000, 0x000000071d800000, 0x000000071da00000|  0%| F|  |TAMS 0x000000071d800000, 0x000000071d800000| Untracked 
| 237|0x000000071da00000, 0x000000071da00000, 0x000000071dc00000|  0%| F|  |TAMS 0x000000071da00000, 0x000000071da00000| Untracked 
| 238|0x000000071dc00000, 0x000000071dc00000, 0x000000071de00000|  0%| F|  |TAMS 0x000000071dc00000, 0x000000071dc00000| Untracked 
| 239|0x000000071de00000, 0x000000071de00000, 0x000000071e000000|  0%| F|  |TAMS 0x000000071de00000, 0x000000071de00000| Untracked 
| 240|0x000000071e000000, 0x000000071e000000, 0x000000071e200000|  0%| F|  |TAMS 0x000000071e000000, 0x000000071e000000| Untracked 
| 241|0x000000071e200000, 0x000000071e200000, 0x000000071e400000|  0%| F|  |TAMS 0x000000071e200000, 0x000000071e200000| Untracked 
| 242|0x000000071e400000, 0x000000071e400000, 0x000000071e600000|  0%| F|  |TAMS 0x000000071e400000, 0x000000071e400000| Untracked 
| 243|0x000000071e600000, 0x000000071e600000, 0x000000071e800000|  0%| F|  |TAMS 0x000000071e600000, 0x000000071e600000| Untracked 
| 244|0x000000071e800000, 0x000000071e800000, 0x000000071ea00000|  0%| F|  |TAMS 0x000000071e800000, 0x000000071e800000| Untracked 
| 245|0x000000071ea00000, 0x000000071ea00000, 0x000000071ec00000|  0%| F|  |TAMS 0x000000071ea00000, 0x000000071ea00000| Untracked 
| 246|0x000000071ec00000, 0x000000071ec00000, 0x000000071ee00000|  0%| F|  |TAMS 0x000000071ec00000, 0x000000071ec00000| Untracked 
| 247|0x000000071ee00000, 0x000000071ee00000, 0x000000071f000000|  0%| F|  |TAMS 0x000000071ee00000, 0x000000071ee00000| Untracked 
| 248|0x000000071f000000, 0x000000071f000000, 0x000000071f200000|  0%| F|  |TAMS 0x000000071f000000, 0x000000071f000000| Untracked 
| 249|0x000000071f200000, 0x000000071f200000, 0x000000071f400000|  0%| F|  |TAMS 0x000000071f200000, 0x000000071f200000| Untracked 
| 250|0x000000071f400000, 0x000000071f400000, 0x000000071f600000|  0%| F|  |TAMS 0x000000071f400000, 0x000000071f400000| Untracked 
| 251|0x000000071f600000, 0x000000071f600000, 0x000000071f800000|  0%| F|  |TAMS 0x000000071f600000, 0x000000071f600000| Untracked 
| 252|0x000000071f800000, 0x000000071f800000, 0x000000071fa00000|  0%| F|  |TAMS 0x000000071f800000, 0x000000071f800000| Untracked 
| 253|0x000000071fa00000, 0x000000071fa00000, 0x000000071fc00000|  0%| F|  |TAMS 0x000000071fa00000, 0x000000071fa00000| Untracked 
| 254|0x000000071fc00000, 0x000000071fc00000, 0x000000071fe00000|  0%| F|  |TAMS 0x000000071fc00000, 0x000000071fc00000| Untracked 
| 255|0x000000071fe00000, 0x000000071fe00000, 0x0000000720000000|  0%| F|  |TAMS 0x000000071fe00000, 0x000000071fe00000| Untracked 
| 256|0x0000000720000000, 0x00000007201f1670, 0x0000000720200000| 97%| E|  |TAMS 0x0000000720000000, 0x0000000720000000| Complete 
| 257|0x0000000720200000, 0x0000000720400000, 0x0000000720400000|100%| E|CS|TAMS 0x0000000720200000, 0x0000000720200000| Complete 
| 258|0x0000000720400000, 0x0000000720600000, 0x0000000720600000|100%| E|CS|TAMS 0x0000000720400000, 0x0000000720400000| Complete 
| 259|0x0000000720600000, 0x0000000720800000, 0x0000000720800000|100%| E|CS|TAMS 0x0000000720600000, 0x0000000720600000| Complete 
| 260|0x0000000720800000, 0x0000000720a00000, 0x0000000720a00000|100%| E|CS|TAMS 0x0000000720800000, 0x0000000720800000| Complete 
| 261|0x0000000720a00000, 0x0000000720c00000, 0x0000000720c00000|100%| E|CS|TAMS 0x0000000720a00000, 0x0000000720a00000| Complete 
| 262|0x0000000720c00000, 0x0000000720e00000, 0x0000000720e00000|100%| E|CS|TAMS 0x0000000720c00000, 0x0000000720c00000| Complete 
| 263|0x0000000720e00000, 0x0000000721000000, 0x0000000721000000|100%| E|CS|TAMS 0x0000000720e00000, 0x0000000720e00000| Complete 
| 264|0x0000000721000000, 0x0000000721200000, 0x0000000721200000|100%| E|CS|TAMS 0x0000000721000000, 0x0000000721000000| Complete 
| 265|0x0000000721200000, 0x0000000721400000, 0x0000000721400000|100%| E|CS|TAMS 0x0000000721200000, 0x0000000721200000| Complete 
| 266|0x0000000721400000, 0x0000000721600000, 0x0000000721600000|100%| E|CS|TAMS 0x0000000721400000, 0x0000000721400000| Complete 
| 267|0x0000000721600000, 0x0000000721800000, 0x0000000721800000|100%| E|CS|TAMS 0x0000000721600000, 0x0000000721600000| Complete 
| 268|0x0000000721800000, 0x0000000721a00000, 0x0000000721a00000|100%| E|CS|TAMS 0x0000000721800000, 0x0000000721800000| Complete 
| 269|0x0000000721a00000, 0x0000000721c00000, 0x0000000721c00000|100%| E|CS|TAMS 0x0000000721a00000, 0x0000000721a00000| Complete 
| 270|0x0000000721c00000, 0x0000000721e00000, 0x0000000721e00000|100%| E|CS|TAMS 0x0000000721c00000, 0x0000000721c00000| Complete 
| 271|0x0000000721e00000, 0x0000000722000000, 0x0000000722000000|100%| E|CS|TAMS 0x0000000721e00000, 0x0000000721e00000| Complete 
| 272|0x0000000722000000, 0x0000000722200000, 0x0000000722200000|100%| E|CS|TAMS 0x0000000722000000, 0x0000000722000000| Complete 
| 273|0x0000000722200000, 0x0000000722400000, 0x0000000722400000|100%| E|CS|TAMS 0x0000000722200000, 0x0000000722200000| Complete 
| 274|0x0000000722400000, 0x0000000722600000, 0x0000000722600000|100%| E|CS|TAMS 0x0000000722400000, 0x0000000722400000| Complete 
| 275|0x0000000722600000, 0x0000000722800000, 0x0000000722800000|100%| E|CS|TAMS 0x0000000722600000, 0x0000000722600000| Complete 
| 276|0x0000000722800000, 0x0000000722a00000, 0x0000000722a00000|100%| E|CS|TAMS 0x0000000722800000, 0x0000000722800000| Complete 
| 277|0x0000000722a00000, 0x0000000722c00000, 0x0000000722c00000|100%| E|CS|TAMS 0x0000000722a00000, 0x0000000722a00000| Complete 
| 278|0x0000000722c00000, 0x0000000722e00000, 0x0000000722e00000|100%| E|CS|TAMS 0x0000000722c00000, 0x0000000722c00000| Complete 
| 279|0x0000000722e00000, 0x0000000723000000, 0x0000000723000000|100%| E|CS|TAMS 0x0000000722e00000, 0x0000000722e00000| Complete 
| 280|0x0000000723000000, 0x0000000723200000, 0x0000000723200000|100%| E|CS|TAMS 0x0000000723000000, 0x0000000723000000| Complete 
| 281|0x0000000723200000, 0x0000000723400000, 0x0000000723400000|100%| E|CS|TAMS 0x0000000723200000, 0x0000000723200000| Complete 
| 282|0x0000000723400000, 0x0000000723600000, 0x0000000723600000|100%| E|CS|TAMS 0x0000000723400000, 0x0000000723400000| Complete 
| 283|0x0000000723600000, 0x0000000723800000, 0x0000000723800000|100%| E|CS|TAMS 0x0000000723600000, 0x0000000723600000| Complete 
| 284|0x0000000723800000, 0x0000000723a00000, 0x0000000723a00000|100%| E|  |TAMS 0x0000000723800000, 0x0000000723800000| Complete 
| 285|0x0000000723a00000, 0x0000000723c00000, 0x0000000723c00000|100%| E|CS|TAMS 0x0000000723a00000, 0x0000000723a00000| Complete 
| 286|0x0000000723c00000, 0x0000000723e00000, 0x0000000723e00000|100%| E|CS|TAMS 0x0000000723c00000, 0x0000000723c00000| Complete 
| 287|0x0000000723e00000, 0x0000000724000000, 0x0000000724000000|100%| E|CS|TAMS 0x0000000723e00000, 0x0000000723e00000| Complete 
| 288|0x0000000724000000, 0x0000000724200000, 0x0000000724200000|100%| E|CS|TAMS 0x0000000724000000, 0x0000000724000000| Complete 
| 289|0x0000000724200000, 0x0000000724400000, 0x0000000724400000|100%| E|CS|TAMS 0x0000000724200000, 0x0000000724200000| Complete 
| 290|0x0000000724400000, 0x0000000724600000, 0x0000000724600000|100%| E|CS|TAMS 0x0000000724400000, 0x0000000724400000| Complete 
| 291|0x0000000724600000, 0x0000000724800000, 0x0000000724800000|100%| E|CS|TAMS 0x0000000724600000, 0x0000000724600000| Complete 
| 292|0x0000000724800000, 0x0000000724a00000, 0x0000000724a00000|100%| E|CS|TAMS 0x0000000724800000, 0x0000000724800000| Complete 
| 293|0x0000000724a00000, 0x0000000724c00000, 0x0000000724c00000|100%| E|CS|TAMS 0x0000000724a00000, 0x0000000724a00000| Complete 
| 294|0x0000000724c00000, 0x0000000724e00000, 0x0000000724e00000|100%| E|CS|TAMS 0x0000000724c00000, 0x0000000724c00000| Complete 
| 295|0x0000000724e00000, 0x0000000725000000, 0x0000000725000000|100%| E|CS|TAMS 0x0000000724e00000, 0x0000000724e00000| Complete 
| 296|0x0000000725000000, 0x0000000725200000, 0x0000000725200000|100%| E|CS|TAMS 0x0000000725000000, 0x0000000725000000| Complete 
| 297|0x0000000725200000, 0x0000000725400000, 0x0000000725400000|100%| E|CS|TAMS 0x0000000725200000, 0x0000000725200000| Complete 
| 298|0x0000000725400000, 0x0000000725600000, 0x0000000725600000|100%| E|CS|TAMS 0x0000000725400000, 0x0000000725400000| Complete 
| 299|0x0000000725600000, 0x0000000725800000, 0x0000000725800000|100%| E|CS|TAMS 0x0000000725600000, 0x0000000725600000| Complete 
| 300|0x0000000725800000, 0x0000000725a00000, 0x0000000725a00000|100%| E|CS|TAMS 0x0000000725800000, 0x0000000725800000| Complete 
| 301|0x0000000725a00000, 0x0000000725c00000, 0x0000000725c00000|100%| E|CS|TAMS 0x0000000725a00000, 0x0000000725a00000| Complete 
| 302|0x0000000725c00000, 0x0000000725e00000, 0x0000000725e00000|100%| E|CS|TAMS 0x0000000725c00000, 0x0000000725c00000| Complete 
| 303|0x0000000725e00000, 0x0000000726000000, 0x0000000726000000|100%| E|CS|TAMS 0x0000000725e00000, 0x0000000725e00000| Complete 
| 304|0x0000000726000000, 0x0000000726200000, 0x0000000726200000|100%| E|CS|TAMS 0x0000000726000000, 0x0000000726000000| Complete 
| 305|0x0000000726200000, 0x0000000726400000, 0x0000000726400000|100%| E|CS|TAMS 0x0000000726200000, 0x0000000726200000| Complete 
| 306|0x0000000726400000, 0x0000000726600000, 0x0000000726600000|100%| E|CS|TAMS 0x0000000726400000, 0x0000000726400000| Complete 
| 307|0x0000000726600000, 0x0000000726800000, 0x0000000726800000|100%| E|CS|TAMS 0x0000000726600000, 0x0000000726600000| Complete 
| 308|0x0000000726800000, 0x0000000726a00000, 0x0000000726a00000|100%| E|CS|TAMS 0x0000000726800000, 0x0000000726800000| Complete 
| 309|0x0000000726a00000, 0x0000000726c00000, 0x0000000726c00000|100%| E|CS|TAMS 0x0000000726a00000, 0x0000000726a00000| Complete 
| 310|0x0000000726c00000, 0x0000000726e00000, 0x0000000726e00000|100%| E|CS|TAMS 0x0000000726c00000, 0x0000000726c00000| Complete 
| 311|0x0000000726e00000, 0x0000000727000000, 0x0000000727000000|100%| E|CS|TAMS 0x0000000726e00000, 0x0000000726e00000| Complete 
| 312|0x0000000727000000, 0x0000000727200000, 0x0000000727200000|100%| E|CS|TAMS 0x0000000727000000, 0x0000000727000000| Complete 
| 313|0x0000000727200000, 0x0000000727400000, 0x0000000727400000|100%| E|CS|TAMS 0x0000000727200000, 0x0000000727200000| Complete 
| 314|0x0000000727400000, 0x0000000727600000, 0x0000000727600000|100%| E|CS|TAMS 0x0000000727400000, 0x0000000727400000| Complete 
| 315|0x0000000727600000, 0x0000000727800000, 0x0000000727800000|100%| E|CS|TAMS 0x0000000727600000, 0x0000000727600000| Complete 
| 316|0x0000000727800000, 0x0000000727a00000, 0x0000000727a00000|100%| E|CS|TAMS 0x0000000727800000, 0x0000000727800000| Complete 
| 317|0x0000000727a00000, 0x0000000727c00000, 0x0000000727c00000|100%| E|CS|TAMS 0x0000000727a00000, 0x0000000727a00000| Complete 
| 318|0x0000000727c00000, 0x0000000727e00000, 0x0000000727e00000|100%| E|CS|TAMS 0x0000000727c00000, 0x0000000727c00000| Complete 
| 319|0x0000000727e00000, 0x0000000728000000, 0x0000000728000000|100%| E|CS|TAMS 0x0000000727e00000, 0x0000000727e00000| Complete 
| 320|0x0000000728000000, 0x0000000728200000, 0x0000000728200000|100%| E|CS|TAMS 0x0000000728000000, 0x0000000728000000| Complete 
| 321|0x0000000728200000, 0x0000000728400000, 0x0000000728400000|100%| E|CS|TAMS 0x0000000728200000, 0x0000000728200000| Complete 
| 322|0x0000000728400000, 0x0000000728600000, 0x0000000728600000|100%| E|CS|TAMS 0x0000000728400000, 0x0000000728400000| Complete 
| 323|0x0000000728600000, 0x0000000728800000, 0x0000000728800000|100%| E|CS|TAMS 0x0000000728600000, 0x0000000728600000| Complete 
| 324|0x0000000728800000, 0x0000000728a00000, 0x0000000728a00000|100%| E|CS|TAMS 0x0000000728800000, 0x0000000728800000| Complete 
| 325|0x0000000728a00000, 0x0000000728c00000, 0x0000000728c00000|100%| E|CS|TAMS 0x0000000728a00000, 0x0000000728a00000| Complete 
| 326|0x0000000728c00000, 0x0000000728e00000, 0x0000000728e00000|100%| E|CS|TAMS 0x0000000728c00000, 0x0000000728c00000| Complete 
| 327|0x0000000728e00000, 0x0000000729000000, 0x0000000729000000|100%| E|CS|TAMS 0x0000000728e00000, 0x0000000728e00000| Complete 
| 328|0x0000000729000000, 0x0000000729200000, 0x0000000729200000|100%| E|CS|TAMS 0x0000000729000000, 0x0000000729000000| Complete 
| 329|0x0000000729200000, 0x0000000729400000, 0x0000000729400000|100%| E|CS|TAMS 0x0000000729200000, 0x0000000729200000| Complete 
| 330|0x0000000729400000, 0x0000000729600000, 0x0000000729600000|100%| E|CS|TAMS 0x0000000729400000, 0x0000000729400000| Complete 
| 331|0x0000000729600000, 0x0000000729800000, 0x0000000729800000|100%| E|CS|TAMS 0x0000000729600000, 0x0000000729600000| Complete 
| 332|0x0000000729800000, 0x0000000729a00000, 0x0000000729a00000|100%| E|CS|TAMS 0x0000000729800000, 0x0000000729800000| Complete 
| 333|0x0000000729a00000, 0x0000000729c00000, 0x0000000729c00000|100%| E|CS|TAMS 0x0000000729a00000, 0x0000000729a00000| Complete 
| 334|0x0000000729c00000, 0x0000000729e00000, 0x0000000729e00000|100%| E|CS|TAMS 0x0000000729c00000, 0x0000000729c00000| Complete 
| 335|0x0000000729e00000, 0x000000072a000000, 0x000000072a000000|100%| E|CS|TAMS 0x0000000729e00000, 0x0000000729e00000| Complete 
| 336|0x000000072a000000, 0x000000072a200000, 0x000000072a200000|100%| E|CS|TAMS 0x000000072a000000, 0x000000072a000000| Complete 
| 337|0x000000072a200000, 0x000000072a400000, 0x000000072a400000|100%| E|CS|TAMS 0x000000072a200000, 0x000000072a200000| Complete 
| 338|0x000000072a400000, 0x000000072a600000, 0x000000072a600000|100%| E|CS|TAMS 0x000000072a400000, 0x000000072a400000| Complete 
| 339|0x000000072a600000, 0x000000072a800000, 0x000000072a800000|100%| E|CS|TAMS 0x000000072a600000, 0x000000072a600000| Complete 
| 340|0x000000072a800000, 0x000000072aa00000, 0x000000072aa00000|100%| E|CS|TAMS 0x000000072a800000, 0x000000072a800000| Complete 
| 341|0x000000072aa00000, 0x000000072ac00000, 0x000000072ac00000|100%| E|CS|TAMS 0x000000072aa00000, 0x000000072aa00000| Complete 
| 342|0x000000072ac00000, 0x000000072ae00000, 0x000000072ae00000|100%| E|CS|TAMS 0x000000072ac00000, 0x000000072ac00000| Complete 
| 343|0x000000072ae00000, 0x000000072b000000, 0x000000072b000000|100%| E|CS|TAMS 0x000000072ae00000, 0x000000072ae00000| Complete 
| 344|0x000000072b000000, 0x000000072b200000, 0x000000072b200000|100%| E|CS|TAMS 0x000000072b000000, 0x000000072b000000| Complete 
| 345|0x000000072b200000, 0x000000072b400000, 0x000000072b400000|100%| E|CS|TAMS 0x000000072b200000, 0x000000072b200000| Complete 
| 346|0x000000072b400000, 0x000000072b600000, 0x000000072b600000|100%| E|CS|TAMS 0x000000072b400000, 0x000000072b400000| Complete 
| 347|0x000000072b600000, 0x000000072b800000, 0x000000072b800000|100%| E|CS|TAMS 0x000000072b600000, 0x000000072b600000| Complete 
| 348|0x000000072b800000, 0x000000072ba00000, 0x000000072ba00000|100%| E|CS|TAMS 0x000000072b800000, 0x000000072b800000| Complete 
| 349|0x000000072ba00000, 0x000000072bc00000, 0x000000072bc00000|100%| E|CS|TAMS 0x000000072ba00000, 0x000000072ba00000| Complete 
| 350|0x000000072bc00000, 0x000000072be00000, 0x000000072be00000|100%| E|CS|TAMS 0x000000072bc00000, 0x000000072bc00000| Complete 
| 351|0x000000072be00000, 0x000000072c000000, 0x000000072c000000|100%| E|CS|TAMS 0x000000072be00000, 0x000000072be00000| Complete 
| 352|0x000000072c000000, 0x000000072c200000, 0x000000072c200000|100%| E|CS|TAMS 0x000000072c000000, 0x000000072c000000| Complete 
| 353|0x000000072c200000, 0x000000072c400000, 0x000000072c400000|100%| E|CS|TAMS 0x000000072c200000, 0x000000072c200000| Complete 
| 354|0x000000072c400000, 0x000000072c600000, 0x000000072c600000|100%| E|CS|TAMS 0x000000072c400000, 0x000000072c400000| Complete 
| 355|0x000000072c600000, 0x000000072c800000, 0x000000072c800000|100%| E|CS|TAMS 0x000000072c600000, 0x000000072c600000| Complete 
| 356|0x000000072c800000, 0x000000072ca00000, 0x000000072ca00000|100%| E|CS|TAMS 0x000000072c800000, 0x000000072c800000| Complete 
| 357|0x000000072ca00000, 0x000000072cc00000, 0x000000072cc00000|100%| E|CS|TAMS 0x000000072ca00000, 0x000000072ca00000| Complete 
| 358|0x000000072cc00000, 0x000000072ce00000, 0x000000072ce00000|100%| E|CS|TAMS 0x000000072cc00000, 0x000000072cc00000| Complete 
| 359|0x000000072ce00000, 0x000000072d000000, 0x000000072d000000|100%| E|CS|TAMS 0x000000072ce00000, 0x000000072ce00000| Complete 
| 360|0x000000072d000000, 0x000000072d200000, 0x000000072d200000|100%| E|CS|TAMS 0x000000072d000000, 0x000000072d000000| Complete 
| 361|0x000000072d200000, 0x000000072d400000, 0x000000072d400000|100%| E|CS|TAMS 0x000000072d200000, 0x000000072d200000| Complete 
| 362|0x000000072d400000, 0x000000072d600000, 0x000000072d600000|100%| E|CS|TAMS 0x000000072d400000, 0x000000072d400000| Complete 
| 363|0x000000072d600000, 0x000000072d800000, 0x000000072d800000|100%| E|CS|TAMS 0x000000072d600000, 0x000000072d600000| Complete 
| 364|0x000000072d800000, 0x000000072da00000, 0x000000072da00000|100%| E|CS|TAMS 0x000000072d800000, 0x000000072d800000| Complete 
| 365|0x000000072da00000, 0x000000072dc00000, 0x000000072dc00000|100%| E|CS|TAMS 0x000000072da00000, 0x000000072da00000| Complete 

Card table byte_map: [0x00000001229fa000,0x00000001231fa000] _byte_map_base: 0x000000011f1fa000

Marking Bits (Prev, Next): (CMBitMap*) 0x00007f9dd881c610, (CMBitMap*) 0x00007f9dd881c650
 Prev Bits: [0x00000001239fa000, 0x00000001279fa000)
 Next Bits: [0x00000001279fa000, 0x000000012b9fa000)

Polling page: 0x000000010ae7d000

Metaspace:

Usage:
  Non-class:    135.11 MB used.
      Class:     20.76 MB used.
       Both:    155.86 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     135.56 MB ( 71%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      21.12 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     156.69 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  8.47 MB
       Class:  10.88 MB
        Both:  19.34 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 192.62 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 1508.
num_arena_deaths: 16.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2504.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 36.
num_chunks_taken_from_freelist: 7432.
num_chunk_merges: 13.
num_chunk_splits: 5273.
num_chunks_enlarged: 3992.
num_inconsistent_stats: 0.

CodeCache: size=262144Kb used=35496Kb max_used=35496Kb free=226647Kb
 bounds [0x00000001121fa000, 0x00000001144aa000, 0x00000001221fa000]
 total_blobs=16532 nmethods=15424 adapters=1035
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 111.379 Thread 0x00007f9da8973000 15415       1       io.micrometer.common.KeyValues::access$200 (5 bytes)
Event: 111.380 Thread 0x00007f9da8973000 nmethod 15415 0x000000011449dd10 code [0x000000011449dea0, 0x000000011449df50]
Event: 115.462 Thread 0x00007f9da8973000 15416       1       java.util.concurrent.locks.LockSupport::setCurrentBlocker (14 bytes)
Event: 115.462 Thread 0x00007f9da8973000 nmethod 15416 0x000000011449e010 code [0x000000011449e1a0, 0x000000011449e2b0]
Event: 116.506 Thread 0x00007f9da8973000 15417       1       java.util.Date::<init> (8 bytes)
Event: 116.507 Thread 0x00007f9da8973000 nmethod 15417 0x000000011449e390 code [0x000000011449e520, 0x000000011449e5f0]
Event: 116.952 Thread 0x00007f9da8973000 15419       1       org.springframework.boot.devtools.filewatch.DirectorySnapshot::filter (72 bytes)
Event: 116.954 Thread 0x00007f9da8973000 nmethod 15419 0x000000011449e710 code [0x000000011449e960, 0x000000011449ee50]
Event: 116.954 Thread 0x00007f9da8973000 15418       1       org.springframework.boot.devtools.filewatch.DirectorySnapshot::equals (46 bytes)
Event: 116.954 Thread 0x00007f9da8973000 nmethod 15418 0x000000011449f490 code [0x000000011449f660, 0x000000011449f910]
Event: 117.568 Thread 0x00007f9da8973000 15420       1       jdk.internal.net.http.HttpClientImpl::isReferenced (24 bytes)
Event: 117.570 Thread 0x00007f9da8973000 nmethod 15420 0x000000011449fc90 code [0x000000011449fe20, 0x000000011449ff90]
Event: 117.570 Thread 0x00007f9da8973000 15422   !   1       jdk.internal.net.http.HttpClientImpl::purgeTimeoutsAndReturnNextDeadline (326 bytes)
Event: 117.572 Thread 0x00007f9da8973000 nmethod 15422 0x00000001144a0190 code [0x00000001144a0540, 0x00000001144a1a90]
Event: 117.572 Thread 0x00007f9da8973000 15423       1       jdk.internal.net.http.HttpClientImpl::facade (11 bytes)
Event: 117.572 Thread 0x00007f9da8973000 nmethod 15423 0x00000001144a2f10 code [0x00000001144a30a0, 0x00000001144a31d0]
Event: 117.572 Thread 0x00007f9da8973000 15421       1       jdk.internal.net.http.ConnectionPool$ExpiryList::purgeMaybeRequired (5 bytes)
Event: 117.572 Thread 0x00007f9da8973000 nmethod 15421 0x00000001144a3310 code [0x00000001144a34a0, 0x00000001144a3570]
Event: 130.663 Thread 0x00007f9da8973000 15424   !   1       org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer::considerStoppingAConsumer (125 bytes)
Event: 130.664 Thread 0x00007f9da8973000 nmethod 15424 0x00000001144a3610 code [0x00000001144a3820, 0x00000001144a3b80]

GC Heap History (20 events):
Event: 20.171 GC heap before
{Heap before GC invocations=55 (full 0):
 garbage-first heap   total 368640K, used 322368K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 95 young (194560K), 1 survivors (2048K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.173 GC heap after
{Heap after GC invocations=56 (full 0):
 garbage-first heap   total 368640K, used 128455K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.305 GC heap before
{Heap before GC invocations=56 (full 0):
 garbage-first heap   total 368640K, used 339399K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 93 young (190464K), 4 survivors (8192K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.306 GC heap after
{Heap after GC invocations=57 (full 0):
 garbage-first heap   total 368640K, used 140474K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 8 survivors (16384K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.417 GC heap before
{Heap before GC invocations=57 (full 0):
 garbage-first heap   total 368640K, used 332986K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 90 young (184320K), 8 survivors (16384K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.418 GC heap after
{Heap after GC invocations=58 (full 0):
 garbage-first heap   total 368640K, used 148958K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 8 survivors (16384K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.503 GC heap before
{Heap before GC invocations=58 (full 0):
 garbage-first heap   total 368640K, used 304606K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 70 young (143360K), 8 survivors (16384K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.505 GC heap after
{Heap after GC invocations=59 (full 0):
 garbage-first heap   total 368640K, used 153550K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.592 GC heap before
{Heap before GC invocations=59 (full 0):
 garbage-first heap   total 368640K, used 329678K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 77 young (157696K), 5 survivors (10240K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.593 GC heap after
{Heap after GC invocations=60 (full 0):
 garbage-first heap   total 368640K, used 151464K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.678 GC heap before
{Heap before GC invocations=60 (full 0):
 garbage-first heap   total 368640K, used 303016K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 66 young (135168K), 4 survivors (8192K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 20.680 GC heap after
{Heap after GC invocations=61 (full 0):
 garbage-first heap   total 749568K, used 150807K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 122190K, committed 122880K, reserved 1179648K
  class space    used 16896K, committed 17216K, reserved 1048576K
}
Event: 21.183 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 749568K, used 662807K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 219 young (448512K), 4 survivors (8192K)
 Metaspace       used 123076K, committed 123776K, reserved 1179648K
  class space    used 16938K, committed 17216K, reserved 1048576K
}
Event: 21.185 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 749568K, used 150326K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 123076K, committed 123776K, reserved 1179648K
  class space    used 16938K, committed 17216K, reserved 1048576K
}
Event: 22.655 GC heap before
{Heap before GC invocations=62 (full 0):
 garbage-first heap   total 749568K, used 590646K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 219 young (448512K), 4 survivors (8192K)
 Metaspace       used 136373K, committed 137152K, reserved 1179648K
  class space    used 18179K, committed 18560K, reserved 1048576K
}
Event: 22.665 GC heap after
{Heap after GC invocations=63 (full 0):
 garbage-first heap   total 749568K, used 163573K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 10 survivors (20480K)
 Metaspace       used 136373K, committed 137152K, reserved 1179648K
  class space    used 18179K, committed 18560K, reserved 1048576K
}
Event: 23.637 GC heap before
{Heap before GC invocations=63 (full 0):
 garbage-first heap   total 749568K, used 591605K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 219 young (448512K), 10 survivors (20480K)
 Metaspace       used 141815K, committed 142592K, reserved 1179648K
  class space    used 18732K, committed 19136K, reserved 1048576K
}
Event: 23.644 GC heap after
{Heap after GC invocations=64 (full 0):
 garbage-first heap   total 749568K, used 174504K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 16 survivors (32768K)
 Metaspace       used 141815K, committed 142592K, reserved 1179648K
  class space    used 18732K, committed 19136K, reserved 1048576K
}
Event: 25.653 GC heap before
{Heap before GC invocations=64 (full 0):
 garbage-first heap   total 749568K, used 592296K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 219 young (448512K), 16 survivors (32768K)
 Metaspace       used 153292K, committed 154176K, reserved 1245184K
  class space    used 20306K, committed 20736K, reserved 1048576K
}
Event: 25.666 GC heap after
{Heap after GC invocations=65 (full 0):
 garbage-first heap   total 749568K, used 189209K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 22 survivors (45056K)
 Metaspace       used 153292K, committed 154176K, reserved 1245184K
  class space    used 20306K, committed 20736K, reserved 1048576K
}

Dll operation events (20 events):
Event: 9.973 Loading shared library /usr/lib/java/libtcnative-1.dylib failed, dlopen(/usr/lib/java/libtcnative-1.dylib, 0x0001): tried: '/usr/lib/java/libtcnative-1.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/usr/lib/java/libtcnative-1.dylib' (no such
Event: 9.973 Loading shared library /usr/lib/java/libtcnative-1.jnilib failed, dlopen(/usr/lib/java/libtcnative-1.jnilib, 0x0001): tried: '/usr/lib/java/libtcnative-1.jnilib' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/usr/lib/java/libtcnative-1.jnilib' (no 
Event: 9.973 Loading shared library /Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/libtcnative-1.dylib failed, dlopen(/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/libtcnative-1.dylib, 0x0001): tried: '/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/lib
Event: 9.973 Loading shared library /Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/libtcnative-1.jnilib failed, dlopen(/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/libtcnative-1.jnilib, 0x0001): tried: '/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/l
Event: 9.973 Loading shared library /Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/liblibtcnative-1.dylib failed, dlopen(/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/liblibtcnative-1.dylib, 0x0001):
Event: 9.973 Loading shared library /Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/liblibtcnative-1.jnilib failed, dlopen(/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/liblibtcnative-1.jnilib, 0x0001
Event: 9.973 Loading shared library /Users/<USER>/Library/Java/Extensions/liblibtcnative-1.dylib failed, dlopen(/Users/<USER>/Library/Java/Extensions/liblibtcnative-1.dylib, 0x0001): tried: '/Users/<USER>/Library/Java/Extensions/liblibtcnative-1.dylib' (no such 
Event: 9.973 Loading shared library /Users/<USER>/Library/Java/Extensions/liblibtcnative-1.jnilib failed, dlopen(/Users/<USER>/Library/Java/Extensions/liblibtcnative-1.jnilib, 0x0001): tried: '/Users/<USER>/Library/Java/Extensions/liblibtcnative-1.jnilib' (no su
Event: 9.973 Loading shared library /Library/Java/Extensions/liblibtcnative-1.dylib failed, dlopen(/Library/Java/Extensions/liblibtcnative-1.dylib, 0x0001): tried: '/Library/Java/Extensions/liblibtcnative-1.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/
Event: 9.973 Loading shared library /Library/Java/Extensions/liblibtcnative-1.jnilib failed, dlopen(/Library/Java/Extensions/liblibtcnative-1.jnilib, 0x0001): tried: '/Library/Java/Extensions/liblibtcnative-1.jnilib' (no such file), '/System/Volumes/Preboot/Cryptexes/
Event: 9.973 Loading shared library /Network/Library/Java/Extensions/liblibtcnative-1.dylib failed, dlopen(/Network/Library/Java/Extensions/liblibtcnative-1.dylib, 0x0001): tried: '/Network/Library/Java/Extensions/liblibtcnative-1.dylib' (no such file), '/System/Volum
Event: 9.973 Loading shared library /Network/Library/Java/Extensions/liblibtcnative-1.jnilib failed, dlopen(/Network/Library/Java/Extensions/liblibtcnative-1.jnilib, 0x0001): tried: '/Network/Library/Java/Extensions/liblibtcnative-1.jnilib' (no such file), '/System/Vo
Event: 9.973 Loading shared library /System/Library/Java/Extensions/liblibtcnative-1.dylib failed, dlopen(/System/Library/Java/Extensions/liblibtcnative-1.dylib, 0x0001): tried: '/System/Library/Java/Extensions/liblibtcnative-1.dylib' (no such file), '/System/Volumes/
Event: 9.973 Loading shared library /System/Library/Java/Extensions/liblibtcnative-1.jnilib failed, dlopen(/System/Library/Java/Extensions/liblibtcnative-1.jnilib, 0x0001): tried: '/System/Library/Java/Extensions/liblibtcnative-1.jnilib' (no such file), '/System/Volum
Event: 9.973 Loading shared library /usr/lib/java/liblibtcnative-1.dylib failed, dlopen(/usr/lib/java/liblibtcnative-1.dylib, 0x0001): tried: '/usr/lib/java/liblibtcnative-1.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/usr/lib/java/liblibtcnative-1.dyl
Event: 9.974 Loading shared library /usr/lib/java/liblibtcnative-1.jnilib failed, dlopen(/usr/lib/java/liblibtcnative-1.jnilib, 0x0001): tried: '/usr/lib/java/liblibtcnative-1.jnilib' (no such file), '/System/Volumes/Preboot/Cryptexes/OS/usr/lib/java/liblibtcnative-1.
Event: 9.974 Loading shared library /Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/liblibtcnative-1.dylib failed, dlopen(/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/liblibtcnative-1.dylib, 0x0001): tried: '/Users/<USER>/Dev/projects/bulkloads/bulkloads-
Event: 9.974 Loading shared library /Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/liblibtcnative-1.jnilib failed, dlopen(/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/liblibtcnative-1.jnilib, 0x0001): tried: '/Users/<USER>/Dev/projects/bulkloads/bulkload
Event: 19.657 Loaded shared library /private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/libzstd-jni-1.5.5-1116505682861281664747.dylib
Event: 96.972 Loaded shared library /private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib

Deoptimization events (20 events):
Event: 103.559 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000113269f64 sp=0x000000030d7d0880
Event: 103.559 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfd80 mode 1
Event: 103.559 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000112dd2b0c sp=0x000000030d7d0910
Event: 103.559 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfde0 mode 1
Event: 129.654 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x000000011444383c sp=0x000000030d7d0400
Event: 129.654 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cf8d8 mode 1
Event: 129.655 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000113391dbc sp=0x000000030d7d04b0
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfa00 mode 1
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000113393e54 sp=0x000000030d7d05c0
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfb28 mode 1
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000113393824 sp=0x000000030d7d06e0
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfba8 mode 1
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x00000001133934ac sp=0x000000030d7d0740
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfc08 mode 1
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x00000001132d591c sp=0x000000030d7d07a0
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfce8 mode 1
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000113269f64 sp=0x000000030d7d0880
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfd80 mode 1
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT PACKING pc=0x0000000112dd2b0c sp=0x000000030d7d0910
Event: 129.656 Thread 0x00007f9d88841c00 DEOPT UNPACKING pc=0x0000000112250e10 sp=0x000000030d7cfde0 mode 1

Classes unloaded (8 events):
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000801000c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor11'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000801000000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor10'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000800fcc000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor9'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000800fb8000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor8'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000800fb4c00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor7'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000800f89800 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor6'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000800f88000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor5'
Event: 18.693 Thread 0x00007f9dd9957350 Unloading class 0x0000000800db4000 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor4'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 96.601 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000727113bb8}: groovy/lang/GroovyObjectSupportBeanInfo> (0x0000000727113bb8) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 96.602 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007271165a0}: java/lang/ObjectBeanInfo> (0x00000007271165a0) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 96.603 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000726e02390}: java/lang/ObjectCustomizer> (0x0000000726e02390) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 96.614 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000726e206b0}: groovy/lang/GroovyObjectSupportCustomizer> (0x0000000726e206b0) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 96.628 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000726e3c918}: groovy/lang/BindingCustomizer> (0x0000000726e3c918) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 96.658 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000726e81430}: groovy/lang/GroovyShellBeanInfo> (0x0000000726e81430) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 96.668 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000726e9aff8}: groovy/lang/GroovyShellCustomizer> (0x0000000726e9aff8) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 99.730 Thread 0x00007f9d8884b000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000724b70c58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000724b70c58) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 771]
Event: 100.454 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000724161998}: SimpleTemplateScript1BeanInfo> (0x0000000724161998) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.463 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x000000072417b188}: groovy/lang/ScriptBeanInfo> (0x000000072417b188) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.471 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007241949a8}: groovy/lang/ScriptCustomizer> (0x00000007241949a8) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.485 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007241addf8}: SimpleTemplateScript1Customizer> (0x00000007241addf8) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.498 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007241d73e8}: java/io/PrintWriterBeanInfo> (0x00000007241d73e8) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.499 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007241d9dd0}: java/io/WriterBeanInfo> (0x00000007241d9dd0) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.500 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007241dc790}: java/io/WriterCustomizer> (0x00000007241dc790) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 100.503 Thread 0x00007f9d8884b000 Exception <a 'java/lang/ClassNotFoundException'{0x00000007241e7e28}: java/io/PrintWriterCustomizer> (0x00000007241e7e28) 
thrown [src/hotspot/share/classfile/systemDictionary.cpp, line 256]
Event: 101.312 Thread 0x00007f9d8884b000 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000723709698}> (0x0000000723709698) 
thrown [src/hotspot/share/runtime/reflection.cpp, line 1127]
Event: 101.312 Thread 0x00007f9d8884b000 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000723709c10}> (0x0000000723709c10) 
thrown [src/hotspot/share/runtime/reflection.cpp, line 1127]
Event: 101.353 Thread 0x00007f9d8884b000 Exception <a 'java/lang/reflect/InvocationTargetException'{0x000000072372e7b8}> (0x000000072372e7b8) 
thrown [src/hotspot/share/runtime/reflection.cpp, line 1127]
Event: 101.366 Thread 0x00007f9d8884b000 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000723733708}> (0x0000000723733708) 
thrown [src/hotspot/share/runtime/reflection.cpp, line 1127]

VM Operations (20 events):
Event: 92.886 Executing VM operation: get/set locals done
Event: 92.886 Executing VM operation: get/set locals
Event: 92.886 Executing VM operation: get/set locals done
Event: 92.887 Executing VM operation: get/set locals
Event: 92.887 Executing VM operation: get/set locals done
Event: 92.888 Executing VM operation: get/set locals
Event: 92.888 Executing VM operation: get/set locals done
Event: 95.329 Executing VM operation: get/set locals
Event: 95.329 Executing VM operation: get/set locals done
Event: 96.535 Executing VM operation: ChangeBreakpoints
Event: 96.554 Executing VM operation: ChangeBreakpoints done
Event: 100.199 Executing VM operation: HandshakeAllThreads
Event: 100.200 Executing VM operation: HandshakeAllThreads done
Event: 100.526 Executing VM operation: HandshakeAllThreads
Event: 100.526 Executing VM operation: HandshakeAllThreads done
Event: 101.529 Executing VM operation: Cleanup
Event: 101.529 Executing VM operation: Cleanup done
Event: 120.822 Executing VM operation: HandshakeAllThreads
Event: 120.822 Executing VM operation: HandshakeAllThreads done
Event: 131.864 Executing VM operation: Cleanup

Events (20 events):
Event: 98.281 loading class java/util/stream/SortedOps$AbstractIntSortingSink done
Event: 98.281 loading class java/util/stream/SortedOps$SizedIntSortingSink done
Event: 100.212 loading class jdk/internal/vm/annotation/Stable
Event: 100.212 loading class jdk/internal/vm/annotation/Stable done
Event: 100.498 loading class java/io/PrintWriterBeanInfo
Event: 100.498 loading class java/io/PrintWriterBeanInfo done
Event: 100.498 loading class java/io/PrintWriterBeanInfo
Event: 100.498 loading class java/io/PrintWriterBeanInfo done
Event: 100.499 loading class java/io/WriterBeanInfo
Event: 100.499 loading class java/io/WriterBeanInfo done
Event: 100.499 loading class java/io/WriterBeanInfo
Event: 100.499 loading class java/io/WriterBeanInfo done
Event: 100.499 loading class java/io/WriterCustomizer
Event: 100.499 loading class java/io/WriterCustomizer done
Event: 100.500 loading class java/io/WriterCustomizer
Event: 100.500 loading class java/io/WriterCustomizer done
Event: 100.503 loading class java/io/PrintWriterCustomizer
Event: 100.503 loading class java/io/PrintWriterCustomizer done
Event: 100.503 loading class java/io/PrintWriterCustomizer
Event: 100.503 loading class java/io/PrintWriterCustomizer done


Dynamic libraries:
0x000000010ae09000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libjli.dylib
0x00007ff8281f3000 	/usr/lib/libz.1.dylib
0x00007ff8282ea000 	/usr/lib/libSystem.B.dylib
0x00007ff8282e4000 	/usr/lib/system/libcache.dylib
0x00007ff82829c000 	/usr/lib/system/libcommonCrypto.dylib
0x00007ff8282c6000 	/usr/lib/system/libcompiler_rt.dylib
0x00007ff8282ba000 	/usr/lib/system/libcopyfile.dylib
0x00007ff81993d000 	/usr/lib/system/libcorecrypto.dylib
0x00007ff819a35000 	/usr/lib/system/libdispatch.dylib
0x00007ff819be6000 	/usr/lib/system/libdyld.dylib
0x00007ff8282da000 	/usr/lib/system/libkeymgr.dylib
0x00007ff82827f000 	/usr/lib/system/libmacho.dylib
0x00007ff8275d1000 	/usr/lib/system/libquarantine.dylib
0x00007ff8282d7000 	/usr/lib/system/libremovefile.dylib
0x00007ff81f613000 	/usr/lib/system/libsystem_asl.dylib
0x00007ff8198d7000 	/usr/lib/system/libsystem_blocks.dylib
0x00007ff819a80000 	/usr/lib/system/libsystem_c.dylib
0x00007ff8282ce000 	/usr/lib/system/libsystem_collections.dylib
0x00007ff82636c000 	/usr/lib/system/libsystem_configuration.dylib
0x00007ff825115000 	/usr/lib/system/libsystem_containermanager.dylib
0x00007ff827e4f000 	/usr/lib/system/libsystem_coreservices.dylib
0x00007ff81d0aa000 	/usr/lib/system/libsystem_darwin.dylib
0x00007ffd1f143000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00007ff8282db000 	/usr/lib/system/libsystem_dnssd.dylib
0x00007ffd1f147000 	/usr/lib/system/libsystem_eligibility.dylib
0x00007ff819a7d000 	/usr/lib/system/libsystem_featureflags.dylib
0x00007ff819c1d000 	/usr/lib/system/libsystem_info.dylib
0x00007ff828207000 	/usr/lib/system/libsystem_m.dylib
0x00007ff8199f1000 	/usr/lib/system/libsystem_malloc.dylib
0x00007ff81f584000 	/usr/lib/system/libsystem_networkextension.dylib
0x00007ff81d4e6000 	/usr/lib/system/libsystem_notify.dylib
0x00007ff826370000 	/usr/lib/system/libsystem_sandbox.dylib
0x00007ffd1f14e000 	/usr/lib/system/libsystem_sanitizers.dylib
0x00007ff8282d3000 	/usr/lib/system/libsystem_secinit.dylib
0x00007ff819b9e000 	/usr/lib/system/libsystem_kernel.dylib
0x00007ff819c13000 	/usr/lib/system/libsystem_platform.dylib
0x00007ff819bda000 	/usr/lib/system/libsystem_pthread.dylib
0x00007ff821344000 	/usr/lib/system/libsystem_symptoms.dylib
0x00007ff819922000 	/usr/lib/system/libsystem_trace.dylib
0x00007ff8282a9000 	/usr/lib/system/libunwind.dylib
0x00007ff8198db000 	/usr/lib/system/libxpc.dylib
0x00007ff819808000 	/usr/lib/libobjc.A.dylib
0x00007ff819c4b000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00007ff82b9a7000 	/usr/lib/swift/libswiftCore.dylib
0x00007ff819b86000 	/usr/lib/libc++abi.dylib
0x00007ff8282b2000 	/usr/lib/liboah.dylib
0x00007ff819b09000 	/usr/lib/libc++.1.dylib
0x00007ff81ac0c000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00007ff83465b000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00007ff81d39f000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00007ff8282ec000 	/usr/lib/libfakelink.dylib
0x00007ff81ce0b000 	/usr/lib/libicucore.A.dylib
0x00007ff8282ef000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00007ff82fe32000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x00007ff81ca3c000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00007ff81d420000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00007ff833ca8000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00007ff92104f000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00007ff81f1e0000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00007ff8236d5000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00007ff81d0b5000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00007ff82246d000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00007ff827e56000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00007ff8283c8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00007ff8212c4000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x00007ff81a0e8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00007ff82995e000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00007ff8236e3000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00007ff81a89a000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00007ff82844d000 	/usr/lib/libapple_nghttp2.dylib
0x00007ff828483000 	/usr/lib/libcompression.dylib
0x00007ff820f2b000 	/usr/lib/libsqlite3.dylib
0x00007ff8223e0000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00007ff81fb19000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00007ff82c5c1000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00007ff82aec4000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00007ff821260000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00007ff81f59e000 	/usr/lib/libenergytrace.dylib
0x00007ff82134c000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00007ff82240f000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00007ff8275f6000 	/usr/lib/libbsm.0.dylib
0x00007ff828283000 	/usr/lib/system/libkxld.dylib
0x00007ffd1e019000 	/usr/lib/libCoreEntitlements.dylib
0x00007ffc1e39a000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x00007ff820f12000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00007ff81f59f000 	/usr/lib/libMobileGestalt.dylib
0x00007ff827e35000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00007ff8275de000 	/usr/lib/libcoretls.dylib
0x00007ff8299cb000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00007ff82847d000 	/usr/lib/libpam.2.dylib
0x00007ff829a45000 	/usr/lib/libxar.1.dylib
0x00007ff82370e000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x00007ff8224f4000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00007ff82833f000 	/usr/lib/libarchive.2.dylib
0x00007ff823756000 	/usr/lib/libxml2.2.dylib
0x00007ff82637d000 	/usr/lib/liblangid.dylib
0x00007ff82dce2000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00007ffb297a5000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x00007ffc2563a000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x00007ffc261aa000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x00007ff923b5b000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00007ff92150e000 	/usr/lib/swift/libswiftDarwin.dylib
0x00007ff831bad000 	/usr/lib/swift/libswiftDispatch.dylib
0x00007ff923bac000 	/usr/lib/swift/libswiftIOKit.dylib
0x00007ffd1ed79000 	/usr/lib/swift/libswiftSystem.dylib
0x00007ff923b6c000 	/usr/lib/swift/libswiftXPC.dylib
0x00007ffd1edf1000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x00007ffd1edf4000 	/usr/lib/swift/libswift_Concurrency.dylib
0x00007ffd1ef3a000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x00007ffd1efd5000 	/usr/lib/swift/libswift_errno.dylib
0x00007ffd1efd7000 	/usr/lib/swift/libswift_math.dylib
0x00007ffd1efda000 	/usr/lib/swift/libswift_signal.dylib
0x00007ffd1efdb000 	/usr/lib/swift/libswift_stdio.dylib
0x00007ffd1efdc000 	/usr/lib/swift/libswift_time.dylib
0x00007ff83465f000 	/usr/lib/swift/libswiftos.dylib
0x00007ffd1efdd000 	/usr/lib/swift/libswiftsys_time.dylib
0x00007ffd1efde000 	/usr/lib/swift/libswiftunistd.dylib
0x00007ff826378000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00007ff923a58000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00007ff8238c7000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x00007ff81f10d000 	/usr/lib/libboringssl.dylib
0x00007ff821334000 	/usr/lib/libdns_services.dylib
0x00007ff922caa000 	/usr/lib/libquic.dylib
0x00007ff82b92f000 	/usr/lib/libusrtcp.dylib
0x00007ffa26330000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x00007ffd1ecb2000 	/usr/lib/swift/libswiftDistributed.dylib
0x00007ff81f1df000 	/usr/lib/libnetwork.dylib
0x00007ff82842b000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00007ffd1ece2000 	/usr/lib/swift/libswiftObservation.dylib
0x00007ff81a72a000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00007ffb38770000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00007ff831e20000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x00007ff827510000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00007ff82648e000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00007ff8272f3000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00007ff825142000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x00007ff82117d000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00007ff831ef1000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x00007ff827503000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00007ff833ae1000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x00007ff825b01000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00007ff81f64a000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x00007ff825553000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00007ff8300dd000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00007ff81f62a000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00007ff83754f000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00007ff82a646000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00007ff81bbef000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00007ff825402000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x00007ff825159000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00007ff825162000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00007ff823836000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00007ff82847b000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x00007ff82a648000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00007ff82302f000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00007ffc24c27000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x00007ff82a68c000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00007ff930661000 	/usr/lib/swift/libswiftMetal.dylib
0x00007ffa1ea0e000 	/usr/lib/swift/libswiftOSLog.dylib
0x00007ff935057000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00007ff9395b1000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00007ff9274c1000 	/usr/lib/swift/libswiftsimd.dylib
0x00007ff8274e9000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00007ff826377000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00007ff8299cd000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00007ffb2642c000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x00007ff829a53000 	/usr/lib/libutil.dylib
0x00007ff8299ac000 	/usr/lib/liblzma.5.dylib
0x00007ff8224fc000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00007ff82fe09000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00007ff829a88000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x00007ff81a55b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00007ff8341c6000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00007ff8203f4000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x00007ff81c0cd000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00007ff82c1ab000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00007ff8205aa000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00007ff82ad8c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00007ff82c58a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00007ff82c583000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00007ff82c181000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00007ff8272c1000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00007ffb2ff61000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00007ff81ec27000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x00007ff82a19f000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00007ff8233d3000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00007ff82a1ef000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x00007ffc21404000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x00007ffd1ecfd000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x00007ffd1ee92000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00007ff82a50f000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00007ff829fc1000 	/usr/lib/libexpat.1.dylib
0x00007ff82ac57000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00007ff82ac84000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00007ff82ad77000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00007ff82a55b000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00007ff829c21000 	/usr/lib/libate.dylib
0x00007ff82ad17000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00007ff82ad0e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x00007ffb34f70000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00007ffb30027000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00007ffa39147000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00007ffb30c3c000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00007ffb2ff62000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00007ff825e97000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x00007ffa220c5000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00007ffc23fb1000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00007ffa39153000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00007ffb1a111000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x00007ff82a190000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00007ffa391a4000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00007ffa39166000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00007ffa39360000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00007ffa3916f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00007ffa39163000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00007ffd1e148000 	/usr/lib/libRosetta.dylib
0x00007ffa3914e000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00007ff8262b1000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00007ff827d8f000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00007ff82767b000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00007ff827bd4000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00007ff82798b000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00007ff827c13000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00007ffb1dbc2000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00007ffb1dba5000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x00007ff81a3d4000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00007ff9289cd000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00007ff93545d000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00007ff923b3a000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00007ff83234a000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00007ff923ada000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00007ff829d58000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x00007ff83230b000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00007ff9346d8000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x00007ff82ad48000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00007ff82c515000 	/usr/lib/libcups.2.dylib
0x00007ff82c5b2000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00007ff82c224000 	/usr/lib/libresolv.9.dylib
0x00007ff828326000 	/usr/lib/libiconv.2.dylib
0x00007ff82a202000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00007ff8345b7000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00007ff829fdc000 	/usr/lib/libheimdal-asn1.dylib
0x00007ff8236ad000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00007ff82c60f000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00007ff8236b9000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00007ff82827e000 	/usr/lib/libcharset.1.dylib
0x00007ffb1a868000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00007ff91de8d000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x00007ff82bf76000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00007ff81c2f1000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x00007ffc1b475000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x00007ff82c169000 	/usr/lib/libAudioStatistics.dylib
0x00007ff8253de000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x00007ff81bd1a000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00007ff8369a9000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00007ff82c110000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00007ff82da6e000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00007ff82a0c6000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00007ffc30bce000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x00007ffa19c3b000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00007ff82a1e4000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00007ff82c595000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00007ff922d94000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00007ff82c435000 	/usr/lib/libSMC.dylib
0x00007ff82ac22000 	/usr/lib/libAudioToolboxUtility.dylib
0x00007ff82c5a3000 	/usr/lib/libperfcheck.dylib
0x00007ffb26a29000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00007ffa2ce2f000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x00007ffa2610b000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00007ff827e40000 	/usr/lib/libbz2.1.0.dylib
0x00007ff923b09000 	/usr/lib/libmis.dylib
0x00007ff82bf36000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x00007ff82ad7d000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00007ff82a1fb000 	/usr/lib/libspindump.dylib
0x00007ffb1bd5d000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00007ff83245a000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x00007ff829eb2000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00007ff830d71000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00007ff830c81000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00007ff8345b8000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00007ff82737e000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x00007ffb2d2b6000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00007ff830129000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00007ff92bc3e000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x00007ffd1de56000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00007ff91df32000 	/usr/lib/libAccessibility.dylib
0x00007ff8270ca000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00007ff828566000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00007ff827665000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00007ff828468000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00007ff828560000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00007ff826494000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00007ff81a823000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00007ffc1c676000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00007ff82ad07000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00007ff82aceb000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00007ff82ad11000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00007ffa26526000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00007ff91cc69000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x00007ffc34156000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00007ff829f73000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00007ff831f99000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x00007ff820a7d000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x00007ff82c77b000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00007ff830e51000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00007ff830dab000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x00007ffb1b789000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00007ff82c578000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x00007ff82a031000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x00007ff82388a000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00007ff923bab000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00007ff829fe5000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00007ff82341f000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x00007ff82ae00000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00007ff825ac9000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00007ff9395c0000 	/usr/lib/swift/libswiftAccelerate.dylib
0x00007ff829d4f000 	/usr/lib/libIOReport.dylib
0x00007ffb2666a000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00007ffd1e18d000 	/usr/lib/libTLE.dylib
0x00007ffa3498a000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x00007ff827606000 	/usr/lib/libmecab.dylib
0x00007ff81a920000 	/usr/lib/libCRFSuite.dylib
0x00007ff82637f000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00007ff827662000 	/usr/lib/libgermantok.dylib
0x00007ff828423000 	/usr/lib/libThaiTokenizer.dylib
0x00007ff82aed4000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00007ff92756b000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00007ff81ea4c000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x00007ff8275d4000 	/usr/lib/libCheckFix.dylib
0x00007ff822411000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x00007ffb389bf000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x00007ff81d3e0000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00007ff82370b000 	/usr/lib/libapp_launch_measurement.dylib
0x00007ff93555f000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00007ff829a57000 	/usr/lib/libxslt.1.dylib
0x00007ff82759a000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00007ff833e56000 	/usr/lib/libcurl.4.dylib
0x00007ffd1e521000 	/usr/lib/libcrypto.46.dylib
0x00007ffd1ea92000 	/usr/lib/libssl.48.dylib
0x00007ff833b3a000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00007ff833b75000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x00007ff82c23d000 	/usr/lib/libsasl2.2.dylib
0x00007ff836b9b000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x00007ff81d551000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00007ffb2977e000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00007ff82f9f7000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00007ff825ac4000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00007ff82af1b000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x00007ffc32785000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00007ffb2091c000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x00007ffc3724a000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x00007ffc363ad000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x00007ff825ab1000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00007ff8257d4000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00007ff82fa2e000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00007ff91bfe9000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00007ff8236a2000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00007ff82fdac000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00007ffb1bc3c000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00007ffb21483000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00007ff92150d000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00007ff83073a000 	/usr/lib/swift/libswiftFoundation.dylib
0x00007ffa34441000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00007ffa227af000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x00007ffc2628b000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00007ff82fa1e000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000010bfce000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/server/libjvm.dylib
0x000000010aec5000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libjimage.dylib
0x000000010af27000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libjdwp.dylib
0x000000010afd3000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libjava.dylib
0x000000010b171000 	/private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x000000010b11d000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libzip.dylib
0x00007ffd1de4c000 	/usr/lib/i18n/libiconv_std.dylib
0x00007ffd1de43000 	/usr/lib/i18n/libUTF8.dylib
0x00007ffd1de51000 	/usr/lib/i18n/libmapper_none.dylib
0x000000012da1c000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libdt_socket.dylib
0x000000012da79000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libnio.dylib
0x000000012dad1000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libnet.dylib
0x000000012dbdb000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libmanagement.dylib
0x000000012dcf2000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libmanagement_ext.dylib
0x000000012ddcf000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libextnet.dylib
0x0000000134613000 	/Users/<USER>/Library/Java/JavaVirtualMachines/jbrsdk-17.0.9/Contents/Home/lib/libverify.dylib
0x000000013a206000 	/private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/libzstd-jni-1.5.5-1116505682861281664747.dylib


VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:61321,suspend=y,server=n -Xmx4G -XX:ReservedCodeCacheSize=256m -Dbulkloads.domain-url=https://curiously-charming-firefly.ngrok-free.app -agentpath:/private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/WebApplication_2025_08_05_220549.jfr,log=/private/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/WebApplication_2025_08_05_220549.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dfile.encoding=UTF-8 
java_command: com.bulkloads.WebApplication
java_class_path (initial): /Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/out/production/classes:/Users/<USER>/Dev/projects/bulkloads/bulkloads-v2/out/production/resources:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-processor/3.2.3/ddfb951196f104bf6c5d2f2b4bd86108ff81abcd/spring-boot-configuration-processor-3.2.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.mapstruct/mapstruct-processor/1.5.5.Final/897f6f115930b936287bef552bf5fe28cc64717d/mapstruct-processor-1.5.5.Final.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.30/f195ee86e6c896ea47a1d39defbe20eb59cd149d/lombok-1.18.30.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok-mapstruct-binding/0.2.0/2f1cf9ced7b720ba4996c942b816669e63f0fa4f/lombok-mapstruct-binding-0.2.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/3.2.3/ccd73fa4c26fd504fe3b2a6af618770f8689a4a8/spring-boot-starter-actuator-3.2.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-data-jpa/3.2.3/1fd0770b23c57627e5a37726fb4060f5bfa4da6f/spring-boot-starter-data-jpa-3.2.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-mail/3.2.3/f9094ea136cd4f67bdf14600049b972607fec465/spring-boot-starter-mail-3.2.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-security/3.2.3/f6353a8e62be7372b3968ab71e613b5188d55c3b/spring-boot-starter-security-3.2.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-validation/3.2.3/6bb79fe1a0760b5cec37bd85e31bd4cc987c8185/spring-boot-starter-validation-3.2.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web/3.2.3/bf2b775d4f4e6349129c64de30939a5493779706/spring-boot-starter-we
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 268435456                              {pd product} {command line}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
   double SweeperThreshold                         = 0.468750                                  {product} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=/Users/<USER>/.rvm/gems/ruby-2.1.1/bin:/Users/<USER>/.rvm/gems/ruby-2.1.1@global/bin:/Users/<USER>/.rvm/rubies/ruby-2.1.1/bin:/Users/<USER>/.local/bin:/Users/<USER>/.local/bin:/Users/<USER>/Dev/tools/apache-maven-3.6.0/bin:/usr/local/opt/libpq/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Applications/VMware Fusion.app/Contents/Public:/Users/<USER>/.cargo/bin:/usr/local/opt/fzf/bin:/Users/<USER>/.lmstudio/bin:/Users/<USER>/.rvm/bin
SHELL=/bin/zsh
DISPLAY=/private/tmp/com.apple.launchd.KegLFMXZ9H/org.macosforge.xquartz:0
LC_CTYPE=en_GB.UTF-8
TERM=xterm-256color
TMPDIR=/var/folders/2t/74vx5drd5qz3_ykwrlh77tmc0000gn/T/

Active Locale:
LC_ALL=C/en_GB.UTF-8/C/C/C/C
LC_COLLATE=C
LC_CTYPE=en_GB.UTF-8
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110100111111111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

JNI global refs:
JNI global refs: 155, weak refs: 22510

JNI global refs memory usage: 221643, weak refs: 226697

OOME stack traces (most recent first):
Classloader memory used:
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 14216K
Loader bootstrap                                                                       : 3254K
Loader org.springframework.boot.devtools.restart.classloader.RestartClassLoader        : 2174K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 157K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 142K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 4819B
Loader groovy.lang.GroovyClassLoader$InnerLoader                                       : 930B
Loader net.bytebuddy.utility.dispatcher.JavaDispatcher$DynamicClassLoader              : 235B

Classes loaded by more than one classloader:
Class com.bulkloads.WebApplication                                                    : loaded 2 times (x 71B)


---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.3.0 Darwin Kernel Version 24.3.0: Thu Jan  2 20:24:23 PST 2025; root:xnu-11215.81.4~3/RELEASE_ARM64_T8122 x86_64
OS uptime: 27 days 9:44 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 10240/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 2.21 2.73 2.98

CPU: (EMULATED) total 8 (initial active 8) (1 cores per cpu, 1 threads per core) family 6 model 44 stepping 0 microcode 0x0, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, tsc, tscinvbit, aes, clmul, clflush
machdep.cpu.brand_string:Apple M3
hw.cpufrequency:2400000000
hw.cpufrequency_min:2400000000
hw.cpufrequency_max:2400000000
hw.cachelinesize:64
hw.l1icachesize:131072
hw.l1dcachesize:65536
hw.l2cachesize:4194304

Memory: 4k page, physical 16777216k(16012k free), swap 12582912k(1242048k free)

vm_info: OpenJDK 64-Bit Server VM (17.0.9+7-b1087.7) for bsd-amd64 JRE (17.0.9+7-b1087.7), built on 2023-11-17 by "builduser" with clang Apple LLVM 13.0.0 (clang-1300.0.29.3)

END.
